<?php

namespace addons\lottery;

use app\common\library\Menu;
use think\Addons;
use think\Console;

/**
 * 插件
 */
class Lottery extends Addons
{
    /**
     * 插件安装方法
     * @return bool
     */
    public function install()
    {
        
        return true;
    }

    /**
     * 插件卸载方法
     * @return bool
     */
    public function uninstall()
    {
        
        return true;
    }

    /**
     * 插件启用方法
     * @return bool
     */
    public function enable()
    {
        
        return true;
    }

    /**
     * 插件禁用方法
     * @return bool
     */
    public function disable()
    {
        
        return true;
    }

    // appInit
    public function appInit() {
        Console::addDefaultCommands([
            'addons\lottery\command\Chat',
            'addons\lottery\command\Test',
            'addons\lottery\command\Draw',
            'addons\lottery\command\Spider',
            'addons\lottery\command\Union'
        ]);
    }

}
