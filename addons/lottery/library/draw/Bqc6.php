<?php

namespace addons\lottery\library\draw;

use addons\lottery\model\IssueBqc6;
use addons\lottery\model\OrderBqc6;
use app\common\model\User;
use addons\lottery\model\Order;
class Bqc6
{

    // 单例
    private static $instance = null;
    private $issue = null; // 未开奖的issue
    private $order_list = []; // 下单数据
    private $draw_match_data = []; // 开奖号码

    // 奖级对照表
    private $bonus_data = [];

    

    // 获取单例
    public static function instance()
    {
        if (self::$instance == null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function __construct()
    {
        
        
    }

    // 开奖
    public function draw()
    {
        // 初始化
        // 获取未开奖的issue
        $this->issue = $this->getUnDrawIssue();
        
        if(!$this->issue){
            return ;
        }
        // 获取所有的下单数据
        $this->order_list = $this->getOrderList();
        if(empty($this->order_list)){
            return ;
        }
        // 获取开奖号码
        $this->draw_match_data = $this->getDrawMatchData();
        if(empty($this->draw_match_data)){
            return ;
        }

        // 获取奖金数据
        $this->bonus_data = $this->getBonusData();
        
        $this->handleOrderList();
    }

    // 获取未开奖的issue
    public function getUnDrawIssue()
    {
        return IssueBqc6::where(['status'=>3])->where(['draw_status'=>0])->order('id', 'desc')->find();
    }

    // 获取所有的下单数据
    public function getOrderList()
    {
        return OrderBqc6::where(['status'=>0])->with(['items'])->where(['issue_id'=>$this->issue->id])->select();
    }

    // 获取开奖号码
    public function getDrawMatchData()
    {
        // 转换为int
        return $this->issue->match_data;
        
    }

    // 获取奖金数据
    public function getBonusData()
    {
        return $this->issue->bonus_data;
    }

    // 处理下单数据
    public function handleOrderList(): void
    {
        // 循环下单数据的item
        foreach ($this->order_list as $order) {
            $total_prize = 0; // 订单总奖金
            
            foreach ($order->items as $item) {
                // 获取投注号码
                $bet_data = $item->bet_data;

                // 判断中奖情况
                $prize_amount = $this->checkPrize($bet_data );

                // 更新投注项状态和奖金
                $this->updateBetItem($item,  $prize_amount);
                
                $total_prize += $prize_amount;
            }
            
            // 更新订单状态和奖金
            $this->updateOrder($order, $total_prize);
        }
        
        // 更新期号开奖状态
        $this->updateIssueDrawStatus();
    }
    
    /**
     * 判断中奖等级
     * @param array $bet_front_numbers 投注前区号码
     * @param array $bet_back_numbers 投注后区号码
     * @return int 中奖等级，0表示未中奖
     */
    private function checkPrize($bet_data )
    {
        $check_result = true;
        $prize_amount = 0;

        foreach ($this->draw_match_data as $key => $match_item) {
            
            $bet_data_item = $bet_data[$key]['half'].','.$bet_data[$key]['full'];
            if ($match_item['drawResult'] != $bet_data_item) {
                $check_result = false;
            }
        }

        // [
        //     {
        //         "awardType": 0,
        //         "group": "10",
        //         "lotteryCondition": "",
        //         "prizeLevel": "一等奖",
        //         "sort": 10,
        //         "stakeAmount": "59,969",
        //         "stakeAmountFormat": "59969",
        //         "stakeCount": "28",
        //         "totalPrizeamount": "1,679,132"
        //     }
        // ]
        if($check_result){
            // 计算奖金
            $prize_amount = $this->bonus_data[0]['stakeAmountFormat'];

        }

        return $prize_amount;
    }
    

    /**
     * 更新投注项状态和奖金
     * @param object $item 投注项
     * @param int $prize_level 中奖等级
     * @param float $prize_amount 奖金金额
     */
    private function updateBetItem($item, $prize_amount)
    {
        // var_dump($prize_amount , $item->bet_multiple);
        // 这里应该实现更新投注项的状态和奖金的逻辑
        // 根据具体的数据模型结构进行操作
        $item->prize_amount = $prize_amount * $item->bet_multiple;
        $item->status = $prize_amount > 0 ? 1 : 2; // 1-已中奖，2-未中奖
        $item->save();
    }
    
    /**
     * 更新订单状态和奖金
     * @param object $order 订单
     * @param float $total_prize 总奖金
     */
    private function updateOrder($order, $total_prize)
    {
        // 使用统一的Order::updateOrderAndReward方法处理订单更新和派奖

        Order::updateOrderAndReward($order, $total_prize, '六场半全场');
    }
    
    /**
     * 更新期号开奖状态
     */
    private function updateIssueDrawStatus()
    {
        // 这里应该实现更新期号开奖状态的逻辑
        $this->issue->draw_status = 1; // 已开奖
        $this->issue->save();
    }


    
}
