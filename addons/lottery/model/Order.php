<?php
namespace addons\lottery\model;

use addons\lottery\library\Exception;
use think\Log;
use think\Db;

class Order extends Base
{
    // 表名
    protected $name = 'lottery_order';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';

    // 追加属性
    protected $append = [
        'status_text',
        'createtime_text',
        'updatetime_text',
        'lottery_type_text'
    ];
    
    // 订单状态
    const STATUS_UNPAID = 0;           // 未付款
    const STATUS_PENDING_TICKET = 1;   // 待出票
    const STATUS_PENDING_DRAW = 2;     // 待开奖
    const STATUS_WON_PENDING_PAYOUT = 3; // 已中奖待派奖
    const STATUS_WON = 3;              // 已中奖（别名，兼容旧代码）
    const STATUS_PAID_OUT = 4;         // 已派奖
    const STATUS_LOST = 5;             // 未中奖
    const STATUS_UNION_ONGOING = 6;    // 认购中
    const STATUS_CANCELED = 9;         // 已取消

    // 合买状态（只记录合买特有状态，其他状态通过订单status判断）
    const UNION_STATUS_NONE = 0;       // 非合买
    const UNION_STATUS_ONGOING = 1;    // 认购中
    const UNION_STATUS_FULL = 2;       // 已满员
    const UNION_STATUS_CANCELED = 3;   // 已撤单

    public static function getStatusList()
    {
        return [
            self::STATUS_UNPAID => '未付款',
            self::STATUS_PENDING_TICKET => '待出票',
            self::STATUS_PENDING_DRAW => '待开奖',
            self::STATUS_WON_PENDING_PAYOUT => '已中奖待派奖',
            self::STATUS_WON => '已中奖待派奖', // 别名，兼容旧代码
            self::STATUS_PAID_OUT => '已派奖',
            self::STATUS_LOST => '未中奖',
            self::STATUS_UNION_ONGOING => '认购中',
            self::STATUS_CANCELED => '已取消'
        ];
    }

    /**
     * 获取合买状态列表
     */
    public static function getUnionStatusList()
    {
        return [
            self::UNION_STATUS_NONE => '非合买',
            self::UNION_STATUS_ONGOING => '认购中',
            self::UNION_STATUS_FULL => '已满员',
            self::UNION_STATUS_CANCELED => '已撤单'
        ];
    }

    /**
     * 更新合买状态
     *
     * @param int $orderId 订单ID
     * @param int $unionStatus 合买状态
     * @return bool
     */
    public static function updateUnionStatus($orderId, $unionStatus)
    {
        return self::where('id', $orderId)->update([
            'union_status' => $unionStatus,
            'updatetime' => time()
        ]);
    }

    /**
     * 批量更新合买状态
     *
     * @param array $orderIds 订单ID数组
     * @param int $unionStatus 合买状态
     * @return bool
     */
    public static function batchUpdateUnionStatus($orderIds, $unionStatus)
    {
        if (empty($orderIds)) {
            return false;
        }

        return self::whereIn('id', $orderIds)->update([
            'union_status' => $unionStatus,
            'updatetime' => time()
        ]);
    }

    // 获取彩票类型
    public function getLotteryTypeTextAttr($value, $data)
    {
        //record.lottery_type === 'football' ? '足球竞彩' : 
        // record.lottery_type === 'basketball' ? '竞彩篮球' :
        // record.lottery_type === 'dlt' ? '大乐透' :
        // record.lottery_type === 'pls' ? '排列三' :
        // record.lottery_type === 'plw' ? '排列五' :
        // record.lottery_type === 'qxc' ? '七星彩' :
        // record.lottery_type === 'jq4' ? '四场进球' :
        // record.lottery_type === 'bqc6' ? '六场半全场' :
        $lotteryTypeList = [
            'football' => '竞彩足球',
            'basketball' => '竞彩篮球',
            'dlt' => '大乐透',
            'pls' => '排列三',
            'plw' => '排列五',
            'qxc' => '七星彩',
            'jq4' => '四场进球',
            'bqc6' => '六场半全场',
            'sfc' => '胜负彩',
            'rx9' => '任选9',
        ];
        return $lotteryTypeList[$data['lottery_type']] ?? '';
    }
    
    public function getStatusTextAttr($value, $data)
    {
        $status = $data['status'] ?? self::STATUS_UNPAID;
        $statusList = self::getStatusList();
        return $statusList[$status] ?? '';
    }
    
    public function getCreatetimeTextAttr($value, $data)
    {
        $value = $value ?: ($data['createtime'] ?? '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    public function getUpdatetimeTextAttr($value, $data)
    {
        $value = $value ?: ($data['updatetime'] ?? '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }
    
    /**
     * 生成订单号
     */
    public static function generateOrderNo()
    {
        return date('YmdHis') . mt_rand(1000, 9999);
    }
    
    /**
     * 创建订单
     * 
     * @param array $data 订单数据
     * @return Order
     * @throws Exception
     */
    public static function createOrder($data)
    {
        if (empty($data['user_id'])) {
            new Exception('用户ID不能为空');
        }
        
        if (empty($data['amount'])) {
            new Exception('订单金额不能为空');
        }
        
        if (empty($data['order_no'])) {
            $data['order_no'] = self::generateOrderNo();
        }
        
        if (!isset($data['status'])) {
            $data['status'] = self::STATUS_UNPAID;
        }
        
        try {
            $order = new self();
            $order->allowField(true)->save($data);
            return $order;
        } catch (\Exception $e) {
            Log::error('创建订单失败：' . $e->getMessage());
            new Exception('创建订单失败');
        }
    }
    
    /**
     * 更新订单状态
     *
     * @param int $id 订单ID
     * @param int $status 订单状态
     * @return bool
     */
    public static function updateStatus($id, $status)
    {
        $order = self::get($id);
        if (!$order) {
            return false;
        }

        $order->status = $status;

        // 合买状态由合买方案状态决定，这里不需要同步更新

        return $order->save();
    }



    /**
     * 更新订单奖金
     * 
     * @param int $id 订单ID
     * @param float $prize_amount 奖金金额
     * @return bool
     */
    public static function updatePrize($id, $prize_amount)
    {
        $order = self::get($id);
        if (!$order) {
            return false;
        }
        $order->prize_amount = $prize_amount;
        return $order->save();
    }
    
    /**
     * 完成订单
     * 
     * @param int $id 订单ID
     * @return bool
     */
    public static function completeOrder($id)
    {
        return self::updateStatus($id, self::STATUS_WON);
    }
    
    /**
     * 取消订单
     * 
     * @param int $id 订单ID
     * @return bool
     */
    public static function cancelOrder($id)
    {
        return self::updateStatus($id, self::STATUS_CANCELED);
    }
    
    /**
     * 退款订单
     * 
     * @param int $id 订单ID
     * @return bool
     */
    public static function refundOrder($id)
    {
        return self::updateStatus($id, self::STATUS_CANCELED);
    }
    
    /**
     * 支付订单
     * 
     * @param int $id 订单ID
     * @param string $paymentMethod 支付方式
     * @param string $transactionId 交易ID
     * @return bool
     */
    public static function payOrder($id, $paymentMethod = '', $transactionId = '')
    {
        $order = self::get($id);
        if (!$order) {
            return false;
        }
        
        $order->status = self::STATUS_PENDING_TICKET;
        $order->payment_method = $paymentMethod;
        $order->transaction_id = $transactionId;
        $order->pay_time = time();
        
        return $order->save();
    }
    
    /**
     * 使用账户余额支付订单
     * 
     * @param int|string $orderIdentifier 订单ID或订单号
     * @param int $userId 用户ID
     * @return array 支付结果，包含订单信息和余额
     */
    public static function pay($orderIdentifier, $userId)
    {
        // 根据ID或订单号查询订单
        $where = [];
        if (is_numeric($orderIdentifier)) {
            $where['id'] = $orderIdentifier;
        } else {
            $where['order_no'] = $orderIdentifier;
        }
        
        $order = self::where($where)->find();
        if (!$order) {
            new Exception('订单不存在');
        }
        
        // 验证订单状态
        if ($order['status'] != self::STATUS_UNPAID) {
            new Exception('订单状态不正确，无法支付');
        }
        
        // 验证订单所属用户
        if ($order['user_id'] != $userId) {
            new Exception('您无权支付此订单');
        }
        
        // 获取用户信息
        $user = \app\common\model\User::get($userId);
        if (!$user) {
            new Exception('用户不存在');
        }
        
        // 验证用户余额是否足够
        if ($user['money'] < $order['total_amount']) {
            new Exception('账户余额不足，请先充值');
        }
        
        Db::startTrans();
        try {
            // 扣减用户余额
            $before = $user['money'];
            $after = $before - $order['total_amount'];
            $result = \app\common\model\User::where('id', $userId)->setDec('money', $order['total_amount']);
            if (!$result) {
                new Exception('扣减余额失败');
            }
            
            // 记录资金变动日志
            $moneyLog = [
                'user_id' => $userId,
                'money' => -$order['total_amount'],
                'before' => $before,
                'after' => $after,
                'memo' => '购买彩票，订单号：' . $order['order_no'],
                'createtime' => time()
            ];
            $result = Db::name('user_money_log')->insert($moneyLog);
            if (!$result) {
                new Exception('记录资金变动失败');
            }
            
            // 更新订单状态为已支付
            $result = self::where('id', $order['id'])->update([
                'status' => self::STATUS_PENDING_TICKET, // 已支付状态
                'updatetime' => time()
            ]);
            if (!$result) {
                new Exception('更新订单状态失败');
            }
            
            // 检查并更新关联的follow记录
            $follow = self::checkOrderFollow($order['id']);
            if ($follow) {
                // 更新follow状态为待出票(状态1表示待出票)
                $result = Follow::where('id', $follow['id'])->update([
                    'status' => Follow::STATUS_PENDING_TICKET, // 待出票状态
                    'updatetime' => time()
                ]);
                if (!$result) {
                    new Exception('更新跟单状态失败');
                }
            }
            
            Db::commit();
            
            // 返回支付结果
            return [
                'order_id' => $order['id'],
                'order_no' => $order['order_no'],
                'amount' => $order['total_amount'],
                'balance' => $after
            ];
        } catch (\Exception $e) {
            Db::rollback();
            new Exception('支付失败：' . $e->getMessage());
        }
    }

    // get ticket_images attr
    public function getTicketImagesAttr($value, $data)
    {
        if (empty($data['ticket_images'])) {
            return [];
        }

        $list = explode(',', $data['ticket_images']);
        foreach ($list as $key => $item) {
            $list[$key] = cdnurl($item, true);
        }
        return $list;
    }
    
    /**
     * 检查订单是否有关联的follow记录
     * 
     * @param int $orderId 订单ID
     * @return array|null follow记录
     */
    public static function checkOrderFollow($orderId)
    {
        return Follow::where('order_id', $orderId)->find();
    }
    
    /**
     * 用户关联
     */
    public function user()
    {
        return $this->belongsTo('User', 'user_id', 'id');
    }

    /**
     * 更新订单状态和奖金（手动派奖模式）
     *
     * @param object $subOrder 子订单对象
     * @param float $totalPrize 总奖金
     * @param string $lotteryType 彩票类型描述（用于记录派奖说明）
     * @return bool
     */
    public static function updateOrderAndReward($subOrder, $totalPrize, $lotteryType = '')
    {
        // 注意：传入的totalPrize已经包含了倍数计算，不需要再次乘以倍数
        // $totalPrize = $totalPrize * $subOrder->bet_multiple; // 移除重复倍数计算

        // 更新子订单
        $subOrder->prize_amount = $totalPrize;
        // 状态：0=未开奖, 1=已开奖未中奖, 2=已开奖已中奖(待派奖), 3=已派奖, 4=已取消
        $subOrder->status = $totalPrize > 0 ? 2 : 1;
        $subOrder->settletime = time();
        $subOrder->save();

        // 获取主订单信息
        $mainOrder = self::get($subOrder->order_id);
        if (!$mainOrder) {
            Log::error('开奖失败：找不到主订单，订单ID=' . $subOrder->order_id);
            return false;
        }

        // 获取彩票类型说明
        if (empty($lotteryType)) {
            $lotteryTypeList = [
                'football' => '竞彩足球',
                'basketball' => '竞彩篮球',
                'dlt' => '大乐透',
                'pls' => '排列三',
                'plw' => '排列五',
                'qxc' => '七星彩',
                'jq4' => '四场进球',
                'bqc6' => '六场半全场',
                'sfc' => '胜负彩',
                'rx9' => '任选9',
            ];
            $lotteryType = $lotteryTypeList[$subOrder->lottery_type] ?? '彩票';
        }

        // ========== 手动派奖模式：移除自动派奖逻辑 ==========
        // 如果中奖，仅记录中奖信息，不进行自动派奖
        if ($totalPrize > 0) {
            echo "订单 {$subOrder->order_id} 中奖金额：{$totalPrize}元，等待手动派奖" . PHP_EOL;

            // 注释掉原有的自动派奖逻辑
            /*
            // 检查是否是合买订单
            $unionPlan = \addons\lottery\model\UnionPlan::where('order_id', $subOrder->order_id)->find();

            if ($unionPlan) {
                // 合买订单处理
                self::handleUnionPrize($mainOrder, $unionPlan, $totalPrize, $lotteryType);
            } else {
                // 检查是否是跟单
                $follow = \addons\lottery\model\Follow::where('order_id', $subOrder->order_id)->find();

                if ($follow) {
                    // 跟单处理
                    self::handleFollowPrize($mainOrder, $follow, $totalPrize, $lotteryType);
                } else {
                    // 普通订单，直接派奖给用户
                    \app\common\model\User::money($totalPrize, $mainOrder->user_id, $lotteryType . '中奖');
                }
            }
            */
        }

        // 更新主订单状态和奖金（需要累计所有子订单的奖金）
        self::updateMainOrderStatusAndPrize($mainOrder->id, $lotteryType);

        return true;
    }

    /**
     * 手动派奖方法
     *
     * @param int $orderId 主订单ID
     * @param float $taxAmount 扣税金额（可选）
     * @param string $remark 派奖备注（可选）
     * @return array 派奖结果
     */
    public static function manualReward($orderId, $taxAmount = 0.00, $remark = '')
    {
        // 获取主订单信息
        $mainOrder = self::where('id', $orderId)->find();
        if (!$mainOrder) {
            return ['code' => 0, 'msg' => '订单不存在'];
        }

        // 检查订单状态
        if ($mainOrder->status != self::STATUS_WON) {
            return ['code' => 0, 'msg' => '订单状态不正确，只能对已中奖订单进行派奖'];
        }

        // 检查奖金金额
        if ($mainOrder->prize_amount <= 0) {
            return ['code' => 0, 'msg' => '订单奖金金额为0，无需派奖'];
        }

        // 计算实际派奖金额（奖金 - 扣税）
        $actualAmount = $mainOrder->prize_amount - $taxAmount;
        if ($actualAmount < 0) {
            return ['code' => 0, 'msg' => '扣税金额不能大于奖金金额'];
        }

        // 获取彩票类型说明
        $lotteryTypeList = [
            'football' => '竞彩足球',
            'basketball' => '竞彩篮球',
            'dlt' => '大乐透',
            'pls' => '排列三',
            'plw' => '排列五',
            'qxc' => '七星彩',
            'jq4' => '四场进球',
            'bqc6' => '六场半全场',
            'sfc' => '胜负彩',
            'rx9' => '任选9',
        ];
        $lotteryType = $lotteryTypeList[$mainOrder->lottery_type] ?? '彩票';

        // 构建派奖说明
        $memo = $lotteryType . '中奖派奖';
        if ($taxAmount > 0) {
            $memo .= '(奖金' . $mainOrder->prize_amount . '元，扣税' . $taxAmount . '元)';
        }
        if (!empty($remark)) {
            $memo .= ' - ' . $remark;
        }

        // 开启事务
        Db::startTrans();
        try {
            // 检查是否是合买订单
            $unionPlan = \addons\lottery\model\UnionPlan::where('order_id', $orderId)->find();

            if ($unionPlan) {
                // 合买订单处理
                $result = self::handleUnionPrizeManual($mainOrder, $unionPlan, $actualAmount, $taxAmount, $lotteryType, $memo);
            } else {
                // 检查是否是跟单
                $follow = \addons\lottery\model\Follow::where('order_id', $orderId)->find();

                if ($follow) {
                    // 跟单处理
                    $result = self::handleFollowPrizeManual($mainOrder, $follow, $actualAmount, $taxAmount, $lotteryType, $memo);
                } else {
                    // 普通订单，直接派奖给用户
                    \app\common\model\User::money($actualAmount, $mainOrder->user_id, $memo);
                    $result = true;
                }
            }

            if (!$result) {
                throw new \Exception('派奖处理失败');
            }

            // 更新订单扣税金额和状态
            $mainOrder->tax_amount = $taxAmount;
            $mainOrder->updatetime = time();
            $mainOrder->save();

            // 更新相关子订单状态为已派奖
            self::updateSubOrderStatusToPaid($orderId, $mainOrder->lottery_type);

            Db::commit();

            return [
                'code' => 1,
                'msg' => '派奖成功',
                'data' => [
                    'order_id' => $orderId,
                    'prize_amount' => $mainOrder->prize_amount,
                    'tax_amount' => $taxAmount,
                    'actual_amount' => $actualAmount,
                    'user_id' => $mainOrder->user_id
                ]
            ];
        } catch (\Exception $e) {
            Db::rollback();
            return ['code' => 0, 'msg' => '派奖失败：' . $e->getMessage()];
        }
    }

    /**
     * 更新子订单状态为已派奖
     *
     * @param int $orderId 主订单ID
     * @param string $lotteryType 彩票类型
     * @return bool
     */
    private static function updateSubOrderStatusToPaid($orderId, $lotteryType)
    {
        // 根据彩票类型确定子订单表
        $subOrderTableMap = [
            'dlt' => 'OrderDlt',
            'pls' => 'OrderPls',
            'plw' => 'OrderPlw',
            'qxc' => 'OrderQxc',
            'jq4' => 'OrderJq4',
            'rx9' => 'OrderRx9',
            'sfc' => 'OrderSfc',
            'football' => 'OrderFootball',
            'basketball' => 'OrderBasketball',
        ];

        if (!isset($subOrderTableMap[$lotteryType])) {
            return false;
        }

        $modelClass = '\\addons\\lottery\\model\\' . $subOrderTableMap[$lotteryType];

        // 更新子订单状态为已派奖(状态3)
        return $modelClass::where('order_id', $orderId)
            ->where('status', 2) // 只更新状态为2(已中奖)的子订单
            ->update(['status' => 3, 'updatetime' => time()]);
    }

    /**
     * 手动派奖 - 处理合买订单
     *
     * @param object $mainOrder 主订单对象
     * @param object $unionPlan 合买计划对象
     * @param float $actualAmount 实际派奖金额
     * @param float $taxAmount 扣税金额
     * @param string $lotteryType 彩票类型
     * @param string $memo 派奖说明
     * @return bool
     */
    private static function handleUnionPrizeManual($mainOrder, $unionPlan, $actualAmount, $taxAmount, $lotteryType, $memo)
    {
        // 获取所有参与者
        $participants = \addons\lottery\model\UnionParticipation::where('union_plan_id', $unionPlan->id)->select();

        if (empty($participants)) {
            throw new \Exception('找不到合买参与者，合买计划ID=' . $unionPlan->id);
        }

        // 计算创建者佣金
        $commissionAmount = 0;
        if ($unionPlan->commission_rate > 0) {
            $commissionAmount = round($actualAmount * ($unionPlan->commission_rate / 100), 2);
            $totalPrizeAfterCommission = $actualAmount - $commissionAmount;
        } else {
            $totalPrizeAfterCommission = $actualAmount;
        }

        // 计算每份奖金
        $prizePerShare = $totalPrizeAfterCommission / $unionPlan->total_shares;

        // 分配奖金给每个参与者
        foreach ($participants as $participant) {
            // 计算该参与者应得的奖金
            $userPrize = round($prizePerShare * $participant->shares, 2);

            // 如果是创建者，加上佣金
            if ($participant->is_creator == 1 && $commissionAmount > 0) {
                $userPrize += $commissionAmount;
                $userMemo = $memo . '（含' . $commissionAmount . '元佣金）';
            } else {
                $userMemo = $memo;
            }

            // 更新用户余额
            \app\common\model\User::money($userPrize, $participant->user_id, $userMemo);

            // 更新合买参与记录状态
            $participant->status = 1; // 已派奖
            $participant->save();
        }

        // 更新合买计划状态
        $unionPlan->status = 2; // 已派奖
        $unionPlan->save();

        return true;
    }

    /**
     * 手动派奖 - 处理跟单订单
     *
     * @param object $mainOrder 主订单对象
     * @param object $follow 跟单记录对象
     * @param float $actualAmount 实际派奖金额
     * @param float $taxAmount 扣税金额
     * @param string $lotteryType 彩票类型
     * @param string $memo 派奖说明
     * @return bool
     */
    private static function handleFollowPrizeManual($mainOrder, $follow, $actualAmount, $taxAmount, $lotteryType, $memo)
    {
        // 检查是否有上级跟单
        if ($follow->parent_follow_id > 0) {
            // 获取上级跟单信息
            $parentFollow = \addons\lottery\model\Follow::get($follow->parent_follow_id);

            if ($parentFollow) {
                // 默认分润比例，可以从系统配置中获取
                $commissionRate = 5; // 假设默认5%

                // 计算分润金额
                $commissionAmount = round($actualAmount * ($commissionRate / 100), 2);
                $userPrize = $actualAmount - $commissionAmount;

                // 给跟单者派奖
                \app\common\model\User::money($userPrize, $follow->user_id, $memo . '(总金额' . $actualAmount . '元,分润' . $commissionAmount . '元)');

                // 给发起人派发分润
                \app\common\model\User::money($commissionAmount, $parentFollow->user_id, $lotteryType . '跟单分润(总金额' . $actualAmount . '元,分润比例' . $commissionRate . '%)');

                // 更新跟单状态
                $follow->status = \addons\lottery\model\Follow::STATUS_WON; // 已中奖状态
                $follow->prize_amount = $userPrize;
                $follow->save();

                return true;
            }
        }

        // 没有上级跟单或找不到上级，直接派奖给用户
        \app\common\model\User::money($actualAmount, $follow->user_id, $memo);

        // 更新跟单状态
        $follow->status = \addons\lottery\model\Follow::STATUS_WON; // 已中奖状态
        $follow->prize_amount = $actualAmount;
        $follow->save();

        return true;
    }

    /**
     * 更新主订单状态和奖金（累计所有子订单）
     *
     * @param int $mainOrderId 主订单ID
     * @param string $lotteryType 彩票类型
     * @return bool
     */
    public static function updateMainOrderStatusAndPrize($mainOrderId, $lotteryType)
    {
        // 获取主订单信息
        $mainOrder = self::where('id', $mainOrderId)->find();
        if (!$mainOrder) {
            echo "错误：找不到主订单，ID: {$mainOrderId}" . PHP_EOL;
            return false;
        }

        // 根据彩票类型获取所有子订单的奖金总和
        $totalPrize = 0;
        $hasWinningSubOrder = false;

        // 根据彩票类型查询对应的子订单表
        $lotteryTypeMap = [
            'dlt' => 'OrderDlt',
            'pls' => 'OrderPls',
            'plw' => 'OrderPlw',
            'qxc' => 'OrderQxc',
            'basketball' => 'OrderBasketball',
            'football' => 'OrderFootball',
            'bqc6' => 'OrderBqc6',
            'jq4' => 'OrderJq4',
            'sfc' => 'OrderSfc',
            'rx9' => 'OrderRx9'
        ];

        if (isset($lotteryTypeMap[$mainOrder->lottery_type])) {
            $subOrderClass = '\\addons\\lottery\\model\\' . $lotteryTypeMap[$mainOrder->lottery_type];

            // 获取该主订单下的所有子订单
            $subOrders = $subOrderClass::where('order_id', $mainOrderId)->select();

            echo "主订单 {$mainOrderId} 下共有 " . count($subOrders) . " 个子订单" . PHP_EOL;

            foreach ($subOrders as $subOrder) {
                echo "子订单 {$subOrder->id}: 奖金 {$subOrder->prize_amount}, 状态 {$subOrder->status}" . PHP_EOL;

                if ($subOrder->prize_amount > 0) {
                    $totalPrize += $subOrder->prize_amount;
                    $hasWinningSubOrder = true;
                }
            }
        }

        // 确定主订单状态
        $mainOrderStatus = $hasWinningSubOrder ? self::STATUS_WON : self::STATUS_LOST;

        echo "主订单累计奖金: {$totalPrize}, 状态: {$mainOrderStatus}" . PHP_EOL;

        // 更新主订单
        $statusResult = self::updateStatus($mainOrderId, $mainOrderStatus);
        $prizeResult = self::updatePrize($mainOrderId, $totalPrize);

        echo "主订单状态更新结果: " . ($statusResult ? '成功' : '失败') . PHP_EOL;
        echo "主订单奖金更新结果: " . ($prizeResult ? '成功' : '失败') . PHP_EOL;

        return $statusResult && $prizeResult;
    }

    /**
     * 处理合买订单的奖金分配
     * 
     * @param object $mainOrder 主订单对象
     * @param object $unionPlan 合买计划对象
     * @param float $totalPrize 总奖金
     * @param string $lotteryType 彩票类型描述
     * @return bool
     */
    protected static function handleUnionPrize($mainOrder, $unionPlan, $totalPrize, $lotteryType)
    {
        // 获取所有参与者
        $participants = \addons\lottery\model\UnionParticipation::where('union_plan_id', $unionPlan->id)->select();
            
        if (empty($participants)) {
            Log::error('派奖失败：找不到合买参与者，合买计划ID=' . $unionPlan->id);
            return false;
        }
        
        // 计算创建者佣金
        $commissionAmount = 0;
        if ($unionPlan->commission_rate > 0) {
            $commissionAmount = round($totalPrize * ($unionPlan->commission_rate / 100), 2);
            $totalPrizeAfterCommission = $totalPrize - $commissionAmount;
        } else {
            $totalPrizeAfterCommission = $totalPrize;
        }
        
        // 计算每份奖金
        $prizePerShare = $totalPrizeAfterCommission / $unionPlan->total_shares;
        
        // 分配奖金给每个参与者
        foreach ($participants as $participant) {
            // 计算该参与者应得的奖金
            $userPrize = round($prizePerShare * $participant->shares, 2);
            
            // 如果是创建者，加上佣金
            if ($participant->is_creator == 1 && $commissionAmount > 0) {
                $userPrize += $commissionAmount;
                $memo = $lotteryType . '合买中奖（含' . $commissionAmount . '元佣金）';
            } else {
                $memo = $lotteryType . '合买中奖';
            }
            
            // 更新用户余额
            \app\common\model\User::money($userPrize, $participant->user_id, $memo);

            // 更新合买参与记录状态（注意：表中没有prize_amount字段）
            $participant->status = 1; // 已派奖
            $participant->save();
        }
        
        // 更新合买计划状态（注意：表中没有prize_amount字段）
        $unionPlan->status = 2; // 已派奖
        $unionPlan->save();
        
        return true;
    }
    
    /**
     * 处理跟单订单的奖金分配
     * 
     * @param object $mainOrder 主订单对象
     * @param object $follow 跟单记录对象
     * @param float $totalPrize 总奖金
     * @param string $lotteryType 彩票类型描述
     * @return bool
     */
    protected static function handleFollowPrize($mainOrder, $follow, $totalPrize, $lotteryType)
    {
        // 检查是否有上级跟单
        if ($follow->parent_follow_id > 0) {
            // 获取上级跟单信息
            $parentFollow = \addons\lottery\model\Follow::get($follow->parent_follow_id);
            
            if ($parentFollow) {
                // 默认分润比例，可以从系统配置中获取
                $commissionRate = 5; // 假设默认5%
                
                // 计算分润金额
                $commissionAmount = round($totalPrize * ($commissionRate / 100), 2);
                $userPrize = $totalPrize - $commissionAmount;
                
                // 给跟单者派奖
                \app\common\model\User::money($userPrize, $follow->user_id, $lotteryType . '跟单中奖(总金额' . $totalPrize . '元,分润' . $commissionAmount . '元)');
                
                // 给发起人派发分润
                \app\common\model\User::money($commissionAmount, $parentFollow->user_id, $lotteryType . '跟单分润(总金额' . $totalPrize . '元,分润比例' . $commissionRate . '%)');
                
                // 更新跟单状态
                $follow->status = \addons\lottery\model\Follow::STATUS_WON; // 已中奖状态
                $follow->prize_amount = $userPrize;
                $follow->save();
                
                return true;
            }
        }
        
        // 没有上级跟单或找不到上级，直接派奖给用户
        \app\common\model\User::money($totalPrize, $follow->user_id, $lotteryType . '跟单中奖');
        
        // 更新跟单状态
        $follow->status = \addons\lottery\model\Follow::STATUS_WON; // 已中奖状态
        $follow->prize_amount = $totalPrize;
        $follow->save();
        
        return true;
    }
} 