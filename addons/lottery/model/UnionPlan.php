<?php
namespace addons\lottery\model;

use think\Exception;
use think\Log;

class UnionPlan extends Base
{
    // 表名
    protected $name = 'lottery_union_plan';

    // 状态常量
    const STATUS_ONGOING = 0;   // 进行中
    const STATUS_FULL = 1;      // 已满员
    const STATUS_ISSUED = 2;    // 已出票
    const STATUS_CANCELED = 3;  // 未满撤单
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    
    // 追加属性
    protected $append = [
        'status_text',
        'createtime_text',
        'updatetime_text',
        'progress',
        'remaining_shares',
        'unit_price',
        'remaining_time',
        'is_expired'
    ];

    /**
     * 获取状态列表
     */
    public static function getStatusList()
    {
        return [
            self::STATUS_ONGOING => '进行中',
            self::STATUS_FULL => '已满员',
            self::STATUS_ISSUED => '已出票',
            self::STATUS_CANCELED => '未满撤单'
        ];
    }
    
    /**
     * 获取状态文字说明
     */
    public function getStatusTextAttr($value, $data)
    {
        $status = $data['status'] ?? self::STATUS_ONGOING;
        $statusList = self::getStatusList();
        return $statusList[$status] ?? '';
    }
    
    /**
     * 创建时间格式化
     */
    public function getCreatetimeTextAttr($value, $data)
    {
        $value = $value ?: ($data['createtime'] ?? '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    /**
     * 更新时间格式化
     */
    public function getUpdatetimeTextAttr($value, $data)
    {
        $value = $value ?: ($data['updatetime'] ?? '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }
    
    /**
     * 获取总金额
     */
    public function getTotalAmountAttr($value, $data)
    {
        if (isset($data['total_amount']) && $data['total_amount']) {
            return $data['total_amount'];
        }
        
        // 如果没有设置，尝试从关联订单获取
        if ($this->order) {
            return $this->order->total_amount;
        }
        
        return 0;
    }
    
    /**
     * 获取进度百分比
     */
    public function getProgressAttr($value, $data)
    {
        if (empty($data['total_shares'])) {
            return 0;
        }
        
        $purchased = isset($data['purchased_shares']) ? $data['purchased_shares'] : 0;
        return round(($purchased / $data['total_shares']) * 100, 2);
    }
    
    /**
     * 获取剩余份数（未支付的份额不占用剩余份数）
     */
    public function getRemainingSharesAttr($value, $data)
    {
        $total = isset($data['total_shares']) ? $data['total_shares'] : 0;
        $purchased = isset($data['purchased_shares']) ? $data['purchased_shares'] : 0;

        // 剩余份数 = 总份数 - 已支付份数（未支付的不占用份额）
        return $total - $purchased;
    }
    
    /**
     * 获取单价
     */
    public function getUnitPriceAttr($value, $data)
    {
        $totalAmount = $this->getTotalAmountAttr(null, $data);
        $totalShares = isset($data['total_shares']) ? $data['total_shares'] : 0;
        
        if (empty($totalShares)) {
            return 0;
        }
        
        return round($totalAmount / $totalShares, 2);
    }
    
    /**
     * 获取剩余时间（秒）
     */
    public function getRemainingTimeAttr($value, $data)
    {
        $deadline = isset($data['deadline']) ? $data['deadline'] : 0;
        $now = time();
        
        if ($deadline <= $now) {
            return 0;
        }
        
        return $deadline - $now;
    }
    
    /**
     * 获取是否已过期
     */
    public function getIsExpiredAttr($value, $data)
    {
        $deadline = isset($data['deadline']) ? $data['deadline'] : 0;
        return time() > $deadline;
    }
    
    /**
     * 完整创建合买方案流程
     * 
     * @param array $params 请求参数
     * @param int $userId 当前用户ID
     * @return UnionPlan
     * @throws Exception
     */
    public static function createUnionPlan($params, $userId)
    {
        // 验证必要参数
        if (empty($params['order_id']) || empty($params['total_shares']) || empty($params['creator_shares'])) {
            new \addons\lottery\library\Exception('缺少必要参数');
        }
        
        // 获取订单信息
        $order = Order::get($params['order_id']);
        if (!$order) {
            new \addons\lottery\library\Exception('订单不存在');
        }
        
        // 检查订单是否属于当前用户
        if ($order['user_id'] != $userId) {
            new \addons\lottery\library\Exception('无权操作此订单');
        }
        
        // 检查订单状态
        if ($order['status'] != Order::STATUS_UNPAID && $order['status'] != Order::STATUS_UNION_ONGOING) {
            new \addons\lottery\library\Exception('订单状态不正确，只有未支付或认购中的订单才能发起合买');
        }
        
        // 验证份数合法性
        if ($params['creator_shares'] > $params['total_shares']) {
            new \addons\lottery\library\Exception('发起人认购份数不能超过总份数');
        }
        
        // 设置默认值
        if (empty($params['guarantee_shares'])) {
            $params['guarantee_shares'] = 0;
        }
        
        if (!isset($params['commission_rate'])) {
            $params['commission_rate'] = 0;
        }
        
        if (!isset($params['visibility'])) {
            $params['visibility'] = 1;
        }
        
        // 获取合买截止时间
        if (empty($params['deadline'])) {
            // 从订单中获取截止时间
            $params['deadline'] = $order['deadline'] ? $order['deadline'] : time() + 12 * 3600;
        }
        
        $params['creator_id'] = $userId;
        $params['purchased_shares'] = 0; // 初始设置为0，只有付款后才增加
        $params['total_amount'] = $order['total_amount'];
        // 计算单份金额
        $params['unit_price'] = self::calculateAmount($params['total_shares'], 1, $params['total_amount']);
        $params['createtime'] = time();
        $params['updatetime'] = time();
        $params['status'] = self::STATUS_ONGOING;
        
        \think\Db::startTrans();
        try {
            // 创建合买方案
            $plan = self::createPlan($params);
            
            // 更新订单为合买订单并设置状态为认购中
            $order->union = 1;  // 标识为合买订单
            $order->status = Order::STATUS_UNION_ONGOING;
            $order->save();

            // 更新合买状态为认购中
            Order::updateUnionStatus($order->id, Order::UNION_STATUS_ONGOING);
            
            \think\Db::commit();
            return $plan;
        } catch (\Exception $e) {
            \think\Db::rollback();
            \think\Log::error('创建合买方案失败: ' . $e->getTraceAsString());
            new \addons\lottery\library\Exception('创建失败: ' . $e->getTraceAsString());
        }
    }
    
    /**
     * 创建合买方案
     * 
     * @param array $data 合买数据
     * @return UnionPlan
     * @throws Exception
     */
    public static function createPlan($data)
    {
        if (empty($data['order_id'])) {
            throw new Exception('订单ID不能为空');
        }
        
        if (empty($data['creator_id'])) {
            throw new Exception('发起人ID不能为空');
        }
        
        if (empty($data['total_shares'])) {
            throw new Exception('总份数不能为空');
        }
        
        if (empty($data['creator_shares'])) {
            throw new Exception('发起人认购份数不能为空');
        }
        
        if (!isset($data['status'])) {
            $data['status'] = self::STATUS_ONGOING;
        }
        
        try {
            $plan = new self();
            $plan->allowField(true)->save($data);
            
            // 保底份额处理
            $guaranteeShares = isset($data['guarantee_shares']) ? $data['guarantee_shares'] : 0;
            $guaranteeAmount = 0;
            
            // 计算单份金额
            $unitPrice = self::calculateAmount($data['total_shares'], 1, $data['total_amount']);
            
            // 如果没有设置单份金额，则更新它
            if (empty($plan->unit_price)) {
                $plan->unit_price = $unitPrice;
                $plan->save();
            }
            
            // 计算认购金额（不含保底）
            $normalAmount = $unitPrice * $data['creator_shares'];
            
            if ($guaranteeShares > 0) {
                $guaranteeAmount = $unitPrice * $guaranteeShares;
            }
            
            // 总支付金额 = 认购金额 + 保底金额
            $totalAmount = $normalAmount + $guaranteeAmount;
            
            // 创建发起人参与记录
            $participation = [
                'union_plan_id' => $plan->id,
                'user_id' => $data['creator_id'],
                'shares' => $data['creator_shares'],
                'old_amount' => $normalAmount, // 保持兼容性
                'amount' => $normalAmount, // 认购金额
                'unit_price' => $unitPrice, // 单份金额
                'is_creator' => 1,
                'is_guarantee' => ($guaranteeShares > 0) ? 1 : 0, // 保持兼容性
                'guarantee_shares' => $guaranteeShares,
                'guarantee_amount' => $guaranteeAmount,
                'total_amount' => $totalAmount, // 总支付金额
                'status' => 0, // 默认未支付
                'createtime' => time()
            ];
            
            UnionParticipation::create($participation);
            
            return $plan;
        } catch (\Exception $e) {
            Log::error('创建合买方案失败：' . $e->getTraceAsString());
            throw new Exception('创建合买方案失败: ' . $e->getTraceAsString());
        }
    }
    
    /**
     * 计算金额
     * 
     * @param int $totalShares 总份数
     * @param int $shares 认购份数
     * @param float $totalAmount 总金额
     * @return float
     */
    public static function calculateAmount($totalShares, $shares, $totalAmount)
    {
        return round(($totalAmount * $shares) / $totalShares, 2);
    }
    
    /**
     * 更新合买状态
     * 
     * @param int $id 合买ID
     * @param int $status 状态
     * @return bool
     */
    public static function updateStatus($id, $status)
    {
        $plan = self::get($id);
        if (!$plan) {
            return false;
        }
        
        $plan->status = $status;
        return $plan->save();
    }
    
    /**
     * 检查是否可以参与
     * 
     * @param int $id 合买ID
     * @param int $shares 认购份数
     * @return bool|string 成功返回true，失败返回错误信息
     */
    public static function checkCanParticipate($id, $shares)
    {
        $plan = self::get($id);
        if (!$plan) {
            return '合买方案不存在';
        }
        
        if ($plan->status != self::STATUS_ONGOING) {
            return '当前合买状态不允许参与';
        }
        
        // 剩余份数计算：不考虑未支付份额（未支付的不占用显示的剩余份数）
        $remainingShares = $plan->total_shares - $plan->purchased_shares;
        
        if ($shares > $remainingShares) {
            return '认购份数超过剩余份数';
        }
        
        if (time() > $plan->deadline) {
            return '已超过认购截止时间';
        }
        
        return true;
    }
    
    /**
     * 更新购买份数
     * 
     * @param int $id 合买ID
     * @param int $shares 新增份数
     * @return bool
     */
    public static function updatePurchasedShares($id, $shares)
    {
        $plan = self::get($id);
        if (!$plan) {
            return false;
        }
        
        $plan->purchased_shares = $plan->purchased_shares + $shares;
        
        // 如果已满，更新状态
        if ($plan->purchased_shares >= $plan->total_shares) {
            $plan->status = self::STATUS_FULL;

            // 同时更新关联订单的状态为待出票
            $order = Order::get($plan->order_id);
            if ($order) {
                $order->status = Order::STATUS_PENDING_TICKET;
                $order->save();

                // 更新合买状态为已满员
                Order::updateUnionStatus($order->id, Order::UNION_STATUS_FULL);
            }
        }
        
        return $plan->save();
    }
    
    /**
     * 关联订单
     */
    public function order()
    {
        return $this->belongsTo('Order', 'order_id', 'id');
    }
    
    /**
     * 关联创建者
     */
    public function creator()
    {
        return $this->belongsTo('User', 'creator_id', 'id');
    }
    
    /**
     * 关联参与记录
     */
    public function participations()
    {
        return $this->hasMany('UnionParticipation', 'union_plan_id', 'id');
    }
} 