<?php
namespace addons\lottery\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use addons\lottery\library\spider\Sfc;
use addons\lottery\library\spider\SfcDraw;
use addons\lottery\library\spider\Bqc6;
use addons\lottery\library\spider\Bqc6Draw;
use addons\lottery\library\spider\Rx9;
use addons\lottery\library\spider\Rx9Draw;
use addons\lottery\library\spider\Dlt;
use addons\lottery\library\spider\Jq4;
use addons\lottery\library\spider\Jq4Draw;
use addons\lottery\library\spider\Pls;
use addons\lottery\library\spider\Plw;
use addons\lottery\library\spider\Qxc;
use addons\lottery\library\spider\Football;
use addons\lottery\library\spider\FootballDraw;
use addons\lottery\library\spider\Basketball;
use addons\lottery\library\spider\BasketballDraw;

use addons\lottery\library\spider\Spider as SpiderLib;
use addons\lottery\library\draw\Draw as DrawLib;
use think\Db;

class Spider extends Command
{
    protected function configure()
    {
        $this->setName('lottery:spider')->setDescription('抓取数据');
    }

    protected function execute(Input $input, Output $output)
    {
        // Db::startTrans();
        // 设置脚本memory_limit
        ini_set('memory_limit', '2048M');
        while (true) {
            try {
                // Bqc6::instance()->getData();
                // Bqc6Draw::instance()->getData();
                // Dlt::instance()->getData();
                // Sfc::instance()->getData();
                // SfcDraw::instance()->getData();
                // Jq4::instance()->getData();
                // Jq4Draw::instance()->getData();
                // Pls::instance()->getData();
                // Plw::instance()->getData();
                // Qxc::instance()->getData();
                // Football::instance()->getData();
                // Rx9::instance()->getData();
                // Rx9Draw::instance()->getData();
                // Football::instance()->getData();
                // FootballDraw::instance()->getData();
                // Basketball::instance()->getData();
                // BasketballDraw::instance()->getData();
    
                SpiderLib::run();
                DrawLib::run();

                // Db::commit();
                
    
            } catch (\Exception $e) {
                $output->writeln($e->getTraceAsString());
                // Db::rollback();
            }

            $output->writeln('等待10秒');
            sleep(10);
        }

    }
}
