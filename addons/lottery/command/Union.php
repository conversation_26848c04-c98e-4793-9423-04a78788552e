<?php
namespace addons\lottery\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\Db;
use addons\lottery\model\UnionPlan;
use addons\lottery\model\UnionParticipation;
use addons\lottery\model\Order;
use app\common\model\User;

/**
 * 合买订单到期处理命令
 * 使用方法：php think lottery:union
 */
class Union extends Command
{
    private $processedCount = 0;
    private $errorCount = 0;
    
    protected function configure()
    {
        $this->setName('lottery:union')
             ->setDescription('合买订单到期处理');
    }

    protected function execute(Input $input, Output $output)
    {
        $output->writeln('开始合买订单到期处理...');
        
        // 设置脚本memory_limit
        ini_set('memory_limit', '1024M');
        
        while (true) {
            try {
                $this->processExpiredUnions($output);
            } catch (\Exception $e) {
                $this->errorCount++;
                $output->writeln('处理异常: ' . $e->getMessage());
            }
            
            $output->writeln('等待30秒...');
            sleep(30);
        }
    }
    
    /**
     * 处理到期的合买订单
     */
    private function processExpiredUnions(Output $output)
    {
        $currentTime = time();
        
        // 查找到期的合买计划（状态为0=进行中，且已过截止时间）
        $expiredPlans = UnionPlan::where('status', 0)
            ->where('deadline', '<', $currentTime)
            ->select();
        
        if (empty($expiredPlans)) {
            $output->writeln('[' . date('Y-m-d H:i:s') . '] 没有到期的合买订单');
            return;
        }
        
        $output->writeln('[' . date('Y-m-d H:i:s') . '] 发现 ' . count($expiredPlans) . ' 个到期的合买订单');
        
        foreach ($expiredPlans as $plan) {
            $this->handleExpiredPlan($plan, $output);
        }
    }
    
    /**
     * 处理单个到期的合买计划
     */
    private function handleExpiredPlan($plan, Output $output)
    {
        Db::startTrans();
        try {
            $output->writeln("处理合买计划 ID: {$plan->id}, 订单ID: {$plan->order_id}");

            // 计算实际购买份数（包含发起人认购份数）
            $actualPurchasedShares = $plan->purchased_shares + $plan->guarantee_shares;

            $output->writeln("购买份数: {$plan->purchased_shares}, 认购份数: {$plan->guarantee_shares}, 总需求份数: {$plan->total_shares}");

            // 检查是否满足最低参与要求
            if ($actualPurchasedShares < $plan->total_shares) {
                // 未满员，需要退款并取消
                $this->refundUnionPlan($plan, $output);
            } else {
                // 满员，标记为待出票状态
                $plan->status = 1; // 成功/待出票状态
                $plan->save();

                // 更新主订单状态为待出票
                Order::where('id', $plan->order_id)->update([
                    'status' => Order::STATUS_PENDING_TICKET,
                    'updatetime' => time()
                ]);

                $output->writeln("合买计划 {$plan->id} 满员成功，订单状态已更新为待出票");
            }

            $this->processedCount++;
            Db::commit();

        } catch (\Exception $e) {
            Db::rollback();
            $this->errorCount++;
            $output->writeln("处理合买计划 {$plan->id} 失败: " . $e->getMessage());
        }
    }
    
    /**
     * 退款合买计划
     */
    private function refundUnionPlan($plan, Output $output)
    {
        // 获取所有参与者
        $participants = UnionParticipation::where('union_plan_id', $plan->id)->select();

        $totalRefundAmount = 0;

        foreach ($participants as $participant) {
            // 计算退款金额（包含普通购买和认购部分）
            $refundAmount = $participant->amount + $participant->guarantee_amount;

            if ($refundAmount > 0) {
                // 退款给用户
                User::money($refundAmount, $participant->user_id, '合买未满员退款');
                $totalRefundAmount += $refundAmount;

                $output->writeln("退款给用户 {$participant->user_id}: {$refundAmount} 元 (普通: {$participant->amount}, 认购: {$participant->guarantee_amount})");
            }
        }

        // 取消主订单
        Order::where('id', $plan->order_id)->update([
            'status' => Order::STATUS_CANCELLED,
            'updatetime' => time()
        ]);

        // 更新合买计划状态为已取消
        $plan->status = 3; // 已取消
        $plan->save();

        $output->writeln("合买计划 {$plan->id} 未满员，已退款 {$totalRefundAmount} 元并取消订单");
    }
}
