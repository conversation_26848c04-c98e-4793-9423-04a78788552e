---
type: "always_apply"
---

# 组件开发规范

## 基本原则
- 使用Vue 3 Composition API
- 组件按功能模块组织，层次清晰
- 基础UI组件与业务组件分离
- 优先使用Vant UI组件，避免重复造轮子

## 组件目录结构
```
src/components/
├── ui/                 # 基础UI组件
│   ├── BaseButton.vue
│   ├── BaseInput.vue
│   └── index.js       # 统一导出
├── common/            # 通用业务组件
├── [module]/          # 按业务模块组织
│   ├── ComponentName.vue
│   └── index.js
└── index.js          # 全局组件注册
```

## 组件命名规范
- 基础UI组件：`Base` + 功能名（如BaseButton）
- 业务组件：模块名 + 功能名（如ChatMessageContainer）
- 文件名使用PascalCase
- 组件内部name使用PascalCase

## 组件模板结构
```vue
<template>
  <div :class="[
    'component-name',
    `component-name--${variant}`,
    { 'component-name--active': isActive }
  ]">
    <!-- 内容 -->
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'

// Props定义
const props = defineProps({
  variant: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'primary'].includes(value)
  }
})

// Emits定义
const emit = defineEmits(['click', 'change'])

// 响应式数据
const isActive = ref(false)

// 计算属性
const computedValue = computed(() => {
  return props.variant === 'primary'
})

// 方法
const handleClick = () => {
  emit('click')
}
</script>

<style scoped>
.component-name {
  /* 组件样式 */
}
</style>
```

## Props规范
- 使用详细的类型定义和验证器
- 提供合理的默认值
- 使用JSDoc注释描述复杂props
- Boolean类型props使用is/has/can前缀

## 事件规范
- 使用defineEmits明确声明所有事件
- 事件名使用kebab-case
- 传递必要的事件参数

## 样式规范
- 使用scoped样式避免污染
- 组件根类名与组件名一致
- 使用BEM命名规范
- 优先使用Tailwind CSS类

## 基础UI组件特点
- 高度可配置和可复用
- 支持多种尺寸和变体
- 内置loading和disabled状态
- 支持插槽扩展

## 业务组件特点
- 封装特定业务逻辑
- 可直接使用store数据
- 处理特定的用户交互
- 可包含多个基础组件

## 图片处理规范
- 图片预览使用Vant的ImagePreview组件
- 图片上传使用Vant的Uploader组件
- 图片加载失败要有fallback处理
- 支持懒加载和压缩优化

## 组件通信
- 父子组件：props + emits
- 跨组件：使用Pinia store
- 兄弟组件：通过共同父组件或store
- 全局事件：使用mitt或store
