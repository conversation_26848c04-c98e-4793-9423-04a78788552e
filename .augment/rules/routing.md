---
type: "always_apply"
---

# 路由开发规范

## 基本原则
- 使用Vue Router 4
- 路由按功能模块组织
- 实现权限控制和路由守卫
- 支持路由懒加载和缓存控制

## 路由配置结构
```javascript
import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/path',
    name: 'RouteName',
    component: () => import('../views/Module/ComponentView.vue'),
    meta: {
      title: '页面标题',
      requiresAuth: true,    // 是否需要登录
      keepAlive: false,      // 是否缓存组件
      roles: ['admin'],      // 需要的角色权限
      hideInMenu: false      // 是否在菜单中隐藏
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})
```

## 路由命名规范
- 路径使用kebab-case：`/user-profile`
- 路由名使用PascalCase：`UserProfile`
- 组件文件名使用PascalCase：`UserProfileView.vue`
- 动态路由参数使用camelCase：`:userId`

## 路由组织模式
```javascript
// 按功能模块组织路由
const routes = [
  // 认证相关
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/auth/LoginView.vue')
  },
  
  // 用户相关
  {
    path: '/user',
    name: 'UserProfile',
    component: () => import('../views/user/UserProfileView.vue'),
    meta: { requiresAuth: true }
  },
  
  // 投注相关
  {
    path: '/bet/:type',
    name: 'Betting',
    component: () => import('../views/betting/BettingView.vue'),
    meta: { requiresAuth: true }
  },
  
  // 投注确认页面
  {
    path: '/bet/:type/confirm',
    name: 'BettingConfirm',
    component: () => import('../views/betting/BettingConfirmView.vue'),
    meta: {
      title: '投注确认',
      requiresAuth: true,
      keepAlive: false
    }
  }
]
```

## 路由守卫配置
```javascript
// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  console.log(`🚀 路由守卫: ${from.path} -> ${to.path}`)

  // 获取登录状态
  const hasToken = !!localStorage.getItem('token')
  const hasUserInfo = !!localStorage.getItem('userInfo')

  // 白名单路由
  const whiteList = ['/login', '/register', '/', '/lottery', '/matches']

  // 白名单路由直接放行
  if (whiteList.includes(to.path)) {
    next()
    return
  }

  // 检查登录状态
  if (!hasToken) {
    next({
      path: '/login',
      query: { redirect: to.fullPath }
    })
    return
  }

  // 初始化用户信息
  const userStore = useUserStore()
  if (!userStore.initialized) {
    try {
      const result = await userStore.initUserInfo()
      if (!result.success) {
        localStorage.removeItem('token')
        localStorage.removeItem('userInfo')
        next({
          path: '/login',
          query: { redirect: to.fullPath }
        })
        return
      }
    } catch (error) {
      console.error('路由守卫异常:', error)
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    }
  }

  // 权限验证通过
  next()
})
```

## 路由元信息使用
```javascript
// 路由配置
{
  path: '/admin',
  name: 'Admin',
  component: AdminView,
  meta: {
    title: '管理后台',
    requiresAuth: true,
    roles: ['admin', 'super_admin'],
    keepAlive: true,
    hideInMenu: false
  }
}

// 在组件中使用
<script setup>
import { useRoute } from 'vue-router'

const route = useRoute()

// 设置页面标题
document.title = route.meta.title || '默认标题'

// 检查权限
const hasPermission = route.meta.roles?.includes(userRole)
</script>
```

## 路由懒加载
```javascript
// 基本懒加载
const routes = [
  {
    path: '/user',
    component: () => import('../views/user/UserView.vue')
  }
]

// 分组懒加载（webpack chunk name）
const routes = [
  {
    path: '/chat/:id',
    component: () => import(/* webpackChunkName: "chat-detail" */ '../views/message/ChatDetailView.vue')
  }
]
```

## 路由缓存控制
```javascript
// 需要缓存的路由
{
  path: '/football-betting',
  name: 'FootballBetting',
  component: () => import('../views/betting/FootballBettingView.vue'),
  meta: {
    keepAlive: true // 使用keep-alive缓存组件状态
  }
}

// 不需要缓存的路由
{
  path: '/user',
  name: 'UserProfile',
  component: () => import('../views/user/UserProfileView.vue'),
  meta: {
    keepAlive: false // 不使用keep-alive缓存
  }
}
```

## 动态路由
```javascript
// 动态路由配置
{
  path: '/user/:id',
  name: 'UserDetail',
  component: UserDetailView,
  props: true // 将路由参数作为props传递给组件
}

// 在组件中接收参数
<script setup>
const props = defineProps({
  id: String
})

// 或者使用useRoute
import { useRoute } from 'vue-router'
const route = useRoute()
const userId = route.params.id
</script>
```

## 路由导航
```javascript
// 编程式导航
import { useRouter } from 'vue-router'

const router = useRouter()

// 基本导航
router.push('/user')
router.push({ name: 'User' })
router.push({ path: '/user', query: { tab: 'profile' } })

// 替换当前路由
router.replace('/login')

// 历史记录导航
router.go(-1) // 后退
router.go(1)  // 前进
```

## 路由错误处理
```javascript
// 路由错误处理
router.onError((error) => {
  console.error('路由错误:', error)

  // 动态导入失败时重新加载页面
  if (error.message.includes('Failed to fetch dynamically imported module')) {
    console.warn('动态导入模块失败，尝试重新加载页面')
    window.location.reload()
  }
})
```

## 页面白名单
```javascript
// 不需要登录的页面
const pageWhiteList = [
  '/login',
  '/register',
  '/',
  '/lottery',
  '/matches'
]

// 不需要登录的API
const apiWhiteList = [
  '/user/login',
  '/user/register',
  '/user/mobilelogin',
  '/sms/send',
  '/lottery/list',
  '/common/'
]
```

## 路由过渡动画
```vue
<template>
  <router-view v-slot="{ Component }">
    <transition name="slide-left" mode="out-in">
      <component :is="Component" />
    </transition>
  </router-view>
</template>

<style>
.slide-left-enter-active,
.slide-left-leave-active {
  transition: transform 0.3s ease;
}

.slide-left-enter-from {
  transform: translateX(100%);
}

.slide-left-leave-to {
  transform: translateX(-100%);
}
</style>
```
