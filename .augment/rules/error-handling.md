---
type: "always_apply"
---

# 错误处理规范

## 基本原则
- 统一的错误处理机制
- 用户友好的错误提示
- 完整的错误日志记录
- 优雅的错误降级处理

## HTTP错误处理
### 拦截器统一处理
```javascript
// 在http.js中已实现统一错误处理
// 组件中无需使用try-catch包装API调用

// ❌ 错误做法
try {
  const result = await userApi.login(data)
} catch (error) {
  // 不需要在组件中处理
}

// ✅ 正确做法
const result = await userApi.login(data)
if (result.success) {
  // 处理成功逻辑
}
```

### 错误类型分类
```javascript
// 错误类型定义
const ERROR_TYPES = {
  AUTH_ERROR: 'auth_error',           // 认证错误
  PERMISSION_ERROR: 'permission_error', // 权限错误
  BUSINESS_ERROR: 'business_error',    // 业务错误
  NETWORK_ERROR: 'network_error',      // 网络错误
  RESPONSE_ERROR: 'response_error',    // 响应错误
  REQUEST_ERROR: 'request_error'       // 请求错误
}

// 错误处理函数
const handleApiError = (error) => {
  switch (error.type) {
    case ERROR_TYPES.AUTH_ERROR:
      // 认证错误不显示toast，自动跳转登录页
      break
      
    case ERROR_TYPES.BUSINESS_ERROR:
      showToast({
        message: error.message || '操作失败，请重试',
        type: 'text',
        position: 'middle'
      })
      break
      
    case ERROR_TYPES.NETWORK_ERROR:
      showToast({
        message: '网络连接异常，请检查您的网络设置',
        type: 'text',
        position: 'middle'
      })
      break
  }
}
```

## 组件错误处理
### ErrorBoundary组件
```vue
<!-- ErrorBoundary.vue -->
<template>
  <div v-if="hasError" class="error-boundary">
    <div class="error-content">
      <van-icon name="warning-o" size="48" color="#ff6b6b" />
      <h3>出现了一些问题</h3>
      <p>{{ errorMessage }}</p>
      <van-button type="primary" @click="retry">重试</van-button>
    </div>
  </div>
  <slot v-else></slot>
</template>

<script setup>
import { ref, onErrorCaptured } from 'vue'

const hasError = ref(false)
const errorMessage = ref('')

const emit = defineEmits(['error'])

onErrorCaptured((error, instance, info) => {
  console.error('组件错误:', error, info)
  
  hasError.value = true
  errorMessage.value = error.message || '未知错误'
  
  emit('error', { error, instance, info })
  
  // 阻止错误继续传播
  return false
})

const retry = () => {
  hasError.value = false
  errorMessage.value = ''
}
</script>
```

### 使用ErrorBoundary
```vue
<template>
  <ErrorBoundary @error="handleComponentError">
    <SomeComponent />
  </ErrorBoundary>
</template>

<script setup>
import ErrorBoundary from '@/components/ErrorBoundary.vue'

const handleComponentError = ({ error, instance, info }) => {
  // 记录错误日志
  console.error('组件渲染错误:', {
    error: error.message,
    component: instance?.$options.name,
    info
  })
}
</script>
```

## 异步操作错误处理
```vue
<script setup>
import { ref } from 'vue'
import { showToast } from 'vant'

const loading = ref(false)
const error = ref(null)

const handleAsyncOperation = async () => {
  loading.value = true
  error.value = null
  
  try {
    const result = await someAsyncOperation()
    // 处理成功结果
    return result
  } catch (err) {
    error.value = err.message
    console.error('异步操作失败:', err)
    
    // 显示用户友好的错误信息
    showToast({
      message: '操作失败，请重试',
      type: 'fail'
    })
  } finally {
    loading.value = false
  }
}
</script>
```

## 表单验证错误处理
```vue
<template>
  <van-form @submit="onSubmit" @failed="onFailed">
    <van-field
      v-model="form.username"
      name="username"
      label="用户名"
      placeholder="请输入用户名"
      :rules="[
        { required: true, message: '请输入用户名' },
        { pattern: /^[a-zA-Z0-9_]{4,16}$/, message: '用户名格式不正确' }
      ]"
    />
    
    <van-field
      v-model="form.password"
      type="password"
      name="password"
      label="密码"
      placeholder="请输入密码"
      :rules="[
        { required: true, message: '请输入密码' },
        { min: 6, message: '密码至少6位' }
      ]"
    />
    
    <van-button
      round
      block
      type="primary"
      native-type="submit"
      :loading="loading"
    >
      登录
    </van-button>
  </van-form>
</template>

<script setup>
import { reactive, ref } from 'vue'
import { showToast } from 'vant'

const form = reactive({
  username: '',
  password: ''
})

const loading = ref(false)

const onSubmit = async (values) => {
  loading.value = true
  
  try {
    const result = await userApi.login(values)
    if (result.success) {
      showToast({ message: '登录成功', type: 'success' })
    }
  } catch (error) {
    // API错误由拦截器处理，这里处理业务逻辑错误
    console.error('登录失败:', error)
  } finally {
    loading.value = false
  }
}

const onFailed = (errorInfo) => {
  console.log('表单验证失败:', errorInfo)
  
  // 显示第一个错误信息
  const firstError = errorInfo.errors[0]
  if (firstError) {
    showToast({
      message: firstError.message,
      type: 'fail'
    })
  }
}
</script>
```

## WebSocket错误处理
```javascript
// WebSocket错误处理
class WebSocketManager {
  constructor() {
    this.ws = null
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 10
  }

  connect() {
    try {
      this.ws = new WebSocket(wsUrl)
      
      this.ws.onopen = () => {
        console.log('WebSocket连接成功')
        this.reconnectAttempts = 0
      }
      
      this.ws.onerror = (error) => {
        console.error('WebSocket错误:', error)
        this.handleConnectionError()
      }
      
      this.ws.onclose = (event) => {
        console.log('WebSocket连接关闭:', event.code, event.reason)
        this.handleConnectionClose(event)
      }
      
    } catch (error) {
      console.error('WebSocket连接失败:', error)
      this.handleConnectionError()
    }
  }

  handleConnectionError() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      console.log(`WebSocket重连第${this.reconnectAttempts}次`)
      
      setTimeout(() => {
        this.connect()
      }, 5000 * this.reconnectAttempts)
    } else {
      console.error('WebSocket重连失败，已达到最大重试次数')
      showToast({
        message: '连接服务器失败，请检查网络',
        type: 'fail'
      })
    }
  }

  handleConnectionClose(event) {
    // 非正常关闭时尝试重连
    if (event.code !== 1000) {
      this.handleConnectionError()
    }
  }
}
```

## 图片加载错误处理
```vue
<template>
  <!-- 使用Vant Image组件的错误处理 -->
  <van-image
    :src="imageUrl"
    :error-icon="'photo-fail'"
    :loading-icon="'photo-o'"
    @error="handleImageError"
  />

  <!-- 自定义图片错误处理 -->
  <img
    :src="imageUrl"
    @error="handleImageError"
    @load="handleImageLoad"
  />
</template>

<script setup>
const handleImageError = (event) => {
  console.error('图片加载失败:', event.target.src)
  
  // 设置默认图片
  event.target.src = '/default-image.png'
}

const handleImageLoad = (event) => {
  console.log('图片加载成功:', event.target.src)
}
</script>
```

## 全局错误处理
```javascript
// main.js
import { createApp } from 'vue'

const app = createApp(App)

// 全局错误处理
app.config.errorHandler = (error, instance, info) => {
  console.error('全局错误:', error, info)
  
  // 发送错误报告到监控服务
  reportError({
    error: error.message,
    stack: error.stack,
    component: instance?.$options.name,
    info
  })
}

// 未捕获的Promise错误
window.addEventListener('unhandledrejection', (event) => {
  console.error('未处理的Promise错误:', event.reason)
  event.preventDefault()
})
```

## 错误日志记录
```javascript
// 错误日志服务
class ErrorLogger {
  static log(error, context = {}) {
    const errorInfo = {
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      ...context
    }
    
    console.error('错误日志:', errorInfo)
    
    // 发送到日志服务
    this.sendToLogService(errorInfo)
  }
  
  static sendToLogService(errorInfo) {
    // 发送错误日志到服务器
    fetch('/api/logs/error', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(errorInfo)
    }).catch(err => {
      console.error('发送错误日志失败:', err)
    })
  }
}
```
