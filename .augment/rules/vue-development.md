---
type: "always_apply"
---

# Vue开发规范

## 基本原则
- 使用Vue 3 + Composition API
- 优先使用`<script setup>`语法
- 合理使用响应式API
- 遵循Vue官方最佳实践

## 项目配置
- 构建工具：Vite
- 状态管理：Pinia
- 路由：Vue Router 4
- UI库：Vant 4.x
- 样式：Tailwind CSS

## Composition API使用规范
```vue
<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'

// 响应式数据
const count = ref(0)
const state = reactive({
  name: '',
  age: 0
})

// 计算属性
const doubleCount = computed(() => count.value * 2)

// 监听器
watch(count, (newVal, oldVal) => {
  console.log(`count changed from ${oldVal} to ${newVal}`)
})

// 生命周期
onMounted(() => {
  console.log('component mounted')
})

// Store使用
const userStore = useUserStore()

// 路由使用
const router = useRouter()
const route = useRoute()
</script>
```

## 响应式数据规范
- 基本类型使用`ref()`
- 对象类型使用`reactive()`或`ref()`
- 避免解构响应式对象，使用`toRefs()`
- 在模板中访问ref无需`.value`

## 计算属性规范
- 用于派生状态的计算
- 保持纯函数特性
- 避免在计算属性中产生副作用
- 复杂计算逻辑可拆分为多个计算属性

## 监听器规范
```javascript
// 基本监听
watch(source, (newVal, oldVal) => {
  // 处理逻辑
})

// 深度监听
watch(source, callback, { deep: true })

// 立即执行
watch(source, callback, { immediate: true })

// 监听多个源
watch([source1, source2], ([new1, new2], [old1, old2]) => {
  // 处理逻辑
})
```

## 生命周期使用
- `onMounted`: 组件挂载后
- `onUpdated`: 组件更新后
- `onUnmounted`: 组件卸载前
- `onBeforeMount`: 组件挂载前
- `onBeforeUpdate`: 组件更新前
- `onBeforeUnmount`: 组件卸载前

## 模板语法规范
```vue
<template>
  <!-- 条件渲染 -->
  <div v-if="condition">条件内容</div>
  <div v-else-if="otherCondition">其他条件</div>
  <div v-else>默认内容</div>

  <!-- 列表渲染 -->
  <div v-for="item in list" :key="item.id">
    {{ item.name }}
  </div>

  <!-- 事件处理 -->
  <button @click="handleClick">点击</button>
  <button @click="handleClickWithParam(item)">带参数点击</button>

  <!-- 双向绑定 -->
  <input v-model="inputValue" />
  
  <!-- 动态属性 -->
  <div :class="{ active: isActive }" :style="dynamicStyle">
    内容
  </div>
</template>
```

## 组件通信规范
```vue
<!-- 父组件 -->
<template>
  <ChildComponent 
    :prop-name="propValue"
    @custom-event="handleCustomEvent"
  />
</template>

<!-- 子组件 -->
<script setup>
const props = defineProps({
  propName: String
})

const emit = defineEmits(['customEvent'])

const handleClick = () => {
  emit('customEvent', eventData)
}
</script>
```

## 插槽使用规范
```vue
<!-- 默认插槽 -->
<template>
  <div class="wrapper">
    <slot></slot>
  </div>
</template>

<!-- 具名插槽 -->
<template>
  <div class="container">
    <header>
      <slot name="header"></slot>
    </header>
    <main>
      <slot></slot>
    </main>
    <footer>
      <slot name="footer"></slot>
    </footer>
  </div>
</template>

<!-- 作用域插槽 -->
<template>
  <div>
    <slot :item="item" :index="index"></slot>
  </div>
</template>
```

## 性能优化
- 使用`v-show`vs`v-if`的场景选择
- 合理使用`keep-alive`缓存组件
- 大列表使用虚拟滚动
- 图片懒加载
- 组件懒加载（路由级别）

## 错误处理
- 使用ErrorBoundary组件捕获错误
- 在关键操作中添加错误处理
- 网络请求错误由拦截器统一处理
