---
type: "always_apply"
---

# 状态管理规范

## 基本原则
- 使用Pinia作为状态管理工具
- Store按功能模块组织
- 合理划分本地状态和全局状态
- 保持状态的不可变性

## Store目录结构
```
src/stores/
├── index.js           # 统一导出
├── user/             # 用户相关
│   ├── index.js
│   └── user.js
├── chat/             # 聊天相关
│   ├── index.js
│   └── chat.js
├── websocket/        # WebSocket相关
│   ├── index.js
│   └── websocket.js
└── [module]/         # 其他业务模块
    ├── index.js
    └── [module].js
```

## Store定义规范
```javascript
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '@/api/module'

export const useModuleStore = defineStore('module', () => {
  // 状态定义
  const state = ref({
    data: [],
    loading: false,
    error: null
  })

  // 计算属性
  const filteredData = computed(() => {
    return state.value.data.filter(item => item.active)
  })

  // 动作方法
  const fetchData = async () => {
    state.value.loading = true
    state.value.error = null
    
    try {
      const response = await api.getData()
      state.value.data = response.data
    } catch (error) {
      state.value.error = error.message
      console.error('获取数据失败:', error)
    } finally {
      state.value.loading = false
    }
  }

  const updateItem = (id, updates) => {
    const index = state.value.data.findIndex(item => item.id === id)
    if (index !== -1) {
      state.value.data[index] = { ...state.value.data[index], ...updates }
    }
  }

  const addItem = (item) => {
    state.value.data.push(item)
  }

  const removeItem = (id) => {
    const index = state.value.data.findIndex(item => item.id === id)
    if (index !== -1) {
      state.value.data.splice(index, 1)
    }
  }

  // 重置状态
  const $reset = () => {
    state.value = {
      data: [],
      loading: false,
      error: null
    }
  }

  return {
    // 状态
    state,
    
    // 计算属性
    filteredData,
    
    // 动作
    fetchData,
    updateItem,
    addItem,
    removeItem,
    $reset
  }
})
```

## 用户Store规范
```javascript
export const useUserStore = defineStore('user', () => {
  // 用户信息
  const user = ref(getInitialUserState())
  const isLoggedIn = ref(getInitialLoginState())
  const initialized = ref(false)

  // 计算属性
  const userInfo = computed(() => user.value)
  const hasPermission = computed(() => (permission) => {
    return user.value.permissions?.includes(permission) || false
  })

  // 登录
  const login = async (credentials) => {
    try {
      const response = await userApi.login(credentials)
      const { token, user: userInfo } = response.data
      
      // 保存到localStorage
      localStorage.setItem('token', token)
      localStorage.setItem('userInfo', JSON.stringify(userInfo))
      
      // 更新状态
      user.value = userInfo
      isLoggedIn.value = true
      initialized.value = true
      
      return { success: true, data: response.data }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  // 退出登录
  const logout = async () => {
    try {
      await userApi.logout()
    } catch (error) {
      console.error('退出登录API调用失败:', error)
    } finally {
      // 清除本地数据
      localStorage.removeItem('token')
      localStorage.removeItem('userInfo')
      
      // 重置状态
      user.value = getInitialUserState()
      isLoggedIn.value = false
      initialized.value = false
    }
  }

  // 从localStorage恢复状态
  const restoreUserStateFromStorage = () => {
    const token = localStorage.getItem('token')
    const savedUser = localStorage.getItem('userInfo')
    
    if (token && savedUser) {
      try {
        user.value = JSON.parse(savedUser)
        isLoggedIn.value = true
        initialized.value = true
        return { success: true }
      } catch (error) {
        console.error('恢复用户状态失败:', error)
        return { success: false, message: '用户数据格式错误' }
      }
    }
    
    return { success: false, message: '无有效的用户数据' }
  }

  return {
    user,
    isLoggedIn,
    initialized,
    userInfo,
    hasPermission,
    login,
    logout,
    restoreUserStateFromStorage
  }
})
```

## Store使用规范
```vue
<script setup>
import { useUserStore } from '@/stores/user'
import { useModuleStore } from '@/stores/module'

// 获取store实例
const userStore = useUserStore()
const moduleStore = useModuleStore()

// 访问状态
const { user, isLoggedIn } = storeToRefs(userStore)
const { state } = storeToRefs(moduleStore)

// 调用动作
const handleLogin = async () => {
  const result = await userStore.login(credentials)
  if (result.success) {
    // 登录成功处理
  }
}

// 监听状态变化
watch(isLoggedIn, (newVal) => {
  if (newVal) {
    // 用户登录后的处理
    moduleStore.fetchData()
  }
})
</script>
```

## 持久化存储
```javascript
// 需要持久化的数据
const persistentData = [
  'token',
  'userInfo',
  'settings',
  'preferences'
]

// 保存到localStorage
const saveToStorage = (key, data) => {
  try {
    localStorage.setItem(key, JSON.stringify(data))
  } catch (error) {
    console.error(`保存${key}到localStorage失败:`, error)
  }
}

// 从localStorage读取
const loadFromStorage = (key, defaultValue = null) => {
  try {
    const data = localStorage.getItem(key)
    return data ? JSON.parse(data) : defaultValue
  } catch (error) {
    console.error(`从localStorage读取${key}失败:`, error)
    return defaultValue
  }
}
```

## 状态同步
```javascript
// WebSocket消息同步状态
const syncStateFromWebSocket = (message) => {
  switch (message.type) {
    case 'USER_UPDATE':
      userStore.updateUser(message.data)
      break
    case 'CHAT_MESSAGE':
      chatStore.addMessage(message.data)
      break
    case 'NOTIFICATION':
      notificationStore.addNotification(message.data)
      break
  }
}
```

## 错误处理
```javascript
// Store中的错误处理
const handleStoreError = (error, context) => {
  console.error(`${context}错误:`, error)
  
  // 设置错误状态
  state.value.error = error.message
  
  // 根据错误类型进行处理
  if (error.type === 'auth_error') {
    userStore.logout()
  }
}
```

## 性能优化
- 使用`storeToRefs`保持响应性
- 避免在store中存储大量数据
- 合理使用计算属性缓存
- 及时清理不需要的状态
