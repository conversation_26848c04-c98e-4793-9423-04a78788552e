---
type: "always_apply"
---

# 测试规范

## 基本原则
- 测试文件创建在根目录`tests/`下
- 按功能模块组织测试文件
- 使用Playwright进行端到端测试
- 保持测试的独立性和可重复性

## 测试目录结构
```
tests/
├── unit/                 # 单元测试
│   ├── components/       # 组件测试
│   ├── utils/           # 工具函数测试
│   ├── stores/          # 状态管理测试
│   └── api/             # API测试
├── e2e/                 # 端到端测试
│   ├── auth/            # 认证流程测试
│   ├── betting/         # 投注流程测试
│   ├── chat/            # 聊天功能测试
│   └── user/            # 用户功能测试
├── fixtures/            # 测试数据
│   ├── users.json
│   ├── bets.json
│   └── messages.json
├── helpers/             # 测试辅助函数
│   ├── setup.js
│   └── utils.js
└── README.md            # 测试说明文档
```

## 测试文件命名规范
- 单元测试：`[filename].test.js`
- 端到端测试：`[feature].spec.js`
- 测试数据：`[module].fixture.js`
- 测试工具：`[utility].helper.js`

## Playwright端到端测试
### 基本配置
```javascript
// playwright.config.js
import { defineConfig, devices } from '@playwright/test'

export default defineConfig({
  testDir: './tests/e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'http://localhost:5173',
    trace: 'on-first-retry',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
  ],
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:5173',
    reuseExistingServer: !process.env.CI,
  },
})
```

### 测试示例
```javascript
// tests/e2e/auth/login.spec.js
import { test, expect } from '@playwright/test'

test.describe('用户登录', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/login')
  })

  test('成功登录', async ({ page }) => {
    // 填写登录表单
    await page.fill('[data-testid="username"]', 'testuser')
    await page.fill('[data-testid="password"]', 'password123')
    
    // 点击登录按钮
    await page.click('[data-testid="login-button"]')
    
    // 验证登录成功
    await expect(page).toHaveURL('/')
    await expect(page.locator('[data-testid="user-avatar"]')).toBeVisible()
  })

  test('登录失败 - 用户名为空', async ({ page }) => {
    await page.fill('[data-testid="password"]', 'password123')
    await page.click('[data-testid="login-button"]')
    
    await expect(page.locator('.van-toast')).toContainText('请输入用户名')
  })

  test('登录失败 - 密码错误', async ({ page }) => {
    await page.fill('[data-testid="username"]', 'testuser')
    await page.fill('[data-testid="password"]', 'wrongpassword')
    await page.click('[data-testid="login-button"]')
    
    await expect(page.locator('.van-toast')).toContainText('用户名或密码错误')
  })
})
```

### 投注流程测试
```javascript
// tests/e2e/betting/football-betting.spec.js
import { test, expect } from '@playwright/test'

test.describe('足球投注', () => {
  test.beforeEach(async ({ page }) => {
    // 登录
    await page.goto('/login')
    await page.fill('[data-testid="username"]', 'testuser')
    await page.fill('[data-testid="password"]', 'password123')
    await page.click('[data-testid="login-button"]')
    
    // 等待登录完成
    await expect(page).toHaveURL('/')
    
    // 进入足球投注页面
    await page.goto('/bet/football')
  })

  test('选择比赛并投注', async ({ page }) => {
    // 等待比赛列表加载
    await expect(page.locator('[data-testid="match-list"]')).toBeVisible()
    
    // 选择第一场比赛
    await page.click('[data-testid="match-item"]:first-child')
    
    // 选择投注选项
    await page.click('[data-testid="bet-option-home"]')
    
    // 输入投注金额
    await page.fill('[data-testid="bet-amount"]', '100')
    
    // 确认投注
    await page.click('[data-testid="confirm-bet"]')
    
    // 验证投注成功
    await expect(page.locator('.van-toast')).toContainText('投注成功')
  })

  test('投注金额验证', async ({ page }) => {
    await page.click('[data-testid="match-item"]:first-child')
    await page.click('[data-testid="bet-option-home"]')
    
    // 测试最小金额限制
    await page.fill('[data-testid="bet-amount"]', '1')
    await page.click('[data-testid="confirm-bet"]')
    await expect(page.locator('.van-toast')).toContainText('最小投注金额为2元')
    
    // 测试最大金额限制
    await page.fill('[data-testid="bet-amount"]', '100000')
    await page.click('[data-testid="confirm-bet"]')
    await expect(page.locator('.van-toast')).toContainText('超出最大投注金额')
  })
})
```

### 聊天功能测试
```javascript
// tests/e2e/chat/chat.spec.js
import { test, expect } from '@playwright/test'

test.describe('聊天功能', () => {
  test.beforeEach(async ({ page }) => {
    // 登录并进入聊天页面
    await page.goto('/login')
    await page.fill('[data-testid="username"]', 'testuser')
    await page.fill('[data-testid="password"]', 'password123')
    await page.click('[data-testid="login-button"]')
    await expect(page).toHaveURL('/')
    
    await page.goto('/messages')
  })

  test('发送文本消息', async ({ page }) => {
    // 进入聊天详情
    await page.click('[data-testid="chat-item"]:first-child')
    
    // 输入消息
    const message = '这是一条测试消息'
    await page.fill('[data-testid="message-input"]', message)
    
    // 发送消息
    await page.click('[data-testid="send-button"]')
    
    // 验证消息发送成功
    await expect(page.locator('[data-testid="message-list"]')).toContainText(message)
  })

  test('发送图片消息', async ({ page }) => {
    await page.click('[data-testid="chat-item"]:first-child')
    
    // 点击图片上传按钮
    await page.click('[data-testid="image-upload-button"]')
    
    // 模拟文件上传
    const fileInput = page.locator('input[type="file"]')
    await fileInput.setInputFiles('tests/fixtures/test-image.jpg')
    
    // 验证图片预览
    await expect(page.locator('[data-testid="image-preview"]')).toBeVisible()
    
    // 发送图片
    await page.click('[data-testid="send-image-button"]')
    
    // 验证图片消息发送成功
    await expect(page.locator('[data-testid="image-message"]')).toBeVisible()
  })
})
```

## 单元测试
### 组件测试
```javascript
// tests/unit/components/BaseButton.test.js
import { mount } from '@vue/test-utils'
import { describe, it, expect } from 'vitest'
import BaseButton from '@/components/ui/BaseButton.vue'

describe('BaseButton', () => {
  it('渲染基本按钮', () => {
    const wrapper = mount(BaseButton, {
      slots: {
        default: '点击我'
      }
    })
    
    expect(wrapper.text()).toBe('点击我')
    expect(wrapper.classes()).toContain('base-button')
  })

  it('处理点击事件', async () => {
    const wrapper = mount(BaseButton)
    
    await wrapper.trigger('click')
    
    expect(wrapper.emitted('click')).toBeTruthy()
  })

  it('禁用状态', () => {
    const wrapper = mount(BaseButton, {
      props: {
        disabled: true
      }
    })
    
    expect(wrapper.attributes('disabled')).toBeDefined()
    expect(wrapper.classes()).toContain('base-button--disabled')
  })
})
```

### API测试
```javascript
// tests/unit/api/user.test.js
import { describe, it, expect, vi } from 'vitest'
import userApi from '@/api/user'
import http from '@/api/http'

// Mock http模块
vi.mock('@/api/http')

describe('userApi', () => {
  it('登录API调用', async () => {
    const mockResponse = {
      code: 1,
      data: {
        token: 'test-token',
        user: { id: 1, name: 'Test User' }
      }
    }
    
    http.post.mockResolvedValue(mockResponse)
    
    const result = await userApi.login({
      username: 'testuser',
      password: 'password123'
    })
    
    expect(http.post).toHaveBeenCalledWith('/user/login', {
      username: 'testuser',
      password: 'password123'
    })
    expect(result).toEqual(mockResponse)
  })
})
```

### Store测试
```javascript
// tests/unit/stores/user.test.js
import { setActivePinia, createPinia } from 'pinia'
import { describe, it, expect, beforeEach } from 'vitest'
import { useUserStore } from '@/stores/user'

describe('useUserStore', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('初始状态', () => {
    const store = useUserStore()
    
    expect(store.isLoggedIn).toBe(false)
    expect(store.user).toEqual({
      name: '游客用户',
      avatar: expect.any(String)
    })
  })

  it('登录成功', async () => {
    const store = useUserStore()
    
    const result = await store.login({
      username: 'testuser',
      password: 'password123'
    })
    
    expect(result.success).toBe(true)
    expect(store.isLoggedIn).toBe(true)
  })
})
```

## 测试数据管理
```javascript
// tests/fixtures/users.js
export const testUsers = {
  validUser: {
    username: 'testuser',
    password: 'password123',
    email: '<EMAIL>'
  },
  adminUser: {
    username: 'admin',
    password: 'admin123',
    role: 'admin'
  }
}

// tests/fixtures/bets.js
export const testBets = {
  footballBet: {
    matchId: 1,
    betType: 'home',
    amount: 100,
    odds: 1.85
  }
}
```

## 测试辅助函数
```javascript
// tests/helpers/setup.js
import { beforeEach } from 'vitest'

// 全局测试设置
beforeEach(() => {
  // 清理localStorage
  localStorage.clear()
  
  // 重置模拟数据
  vi.clearAllMocks()
})

// tests/helpers/utils.js
export const loginUser = async (page, username = 'testuser', password = 'password123') => {
  await page.goto('/login')
  await page.fill('[data-testid="username"]', username)
  await page.fill('[data-testid="password"]', password)
  await page.click('[data-testid="login-button"]')
  await page.waitForURL('/')
}

export const waitForToast = async (page, message) => {
  await expect(page.locator('.van-toast')).toContainText(message)
}
```

## 测试运行命令
```json
{
  "scripts": {
    "test": "vitest",
    "test:unit": "vitest run tests/unit",
    "test:e2e": "playwright test",
    "test:e2e:ui": "playwright test --ui",
    "test:coverage": "vitest --coverage"
  }
}
```
