---
type: "always_apply"
---

# 文件组织规范

## 基本原则
- 按功能模块组织文件结构
- 保持目录层次清晰合理
- 使用一致的命名约定
- 便于维护和扩展

## 项目根目录结构
```
lottery-client/
├── .augment/              # Augment规则文件
│   └── rules/
├── dist/                  # 构建输出目录
├── doc/                   # 项目文档
├── node_modules/          # 依赖包
├── public/                # 静态资源
├── sql/                   # 数据库脚本
├── src/                   # 源代码
├── tests/                 # 测试文件
├── index.html             # HTML模板
├── package.json           # 项目配置
├── tailwind.config.js     # Tailwind配置
├── vite.config.js         # Vite配置
└── README.md              # 项目说明
```

## src目录结构
```
src/
├── api/                   # API接口
│   ├── http.js           # HTTP配置
│   ├── index.js          # 统一导出
│   ├── user.js           # 用户相关API
│   ├── chat.js           # 聊天相关API
│   └── [module].js       # 其他模块API
├── assets/               # 静态资源
│   ├── css/             # 样式文件
│   ├── js/              # JS工具文件
│   └── images/          # 图片资源
├── components/           # 组件
│   ├── ui/              # 基础UI组件
│   ├── common/          # 通用业务组件
│   ├── [module]/        # 模块特定组件
│   └── index.js         # 组件注册
├── config/              # 配置文件
│   ├── index.js         # 主配置
│   └── [module].js      # 模块配置
├── models/              # 数据模型
├── plugins/             # 插件配置
├── router/              # 路由配置
│   └── index.js
├── stores/              # 状态管理
│   ├── index.js         # 统一导出
│   ├── user/            # 用户状态
│   ├── chat/            # 聊天状态
│   └── [module]/        # 其他模块状态
├── utils/               # 工具函数
│   ├── index.js         # 统一导出
│   ├── constants.js     # 常量定义
│   ├── date.js          # 日期工具
│   └── [utility].js     # 其他工具
├── views/               # 页面组件
│   ├── auth/            # 认证相关页面
│   ├── user/            # 用户相关页面
│   ├── betting/         # 投注相关页面
│   ├── message/         # 消息相关页面
│   └── [module]/        # 其他模块页面
├── App.vue              # 根组件
├── main.js              # 入口文件
└── style.css            # 全局样式
```

## 命名约定
### 文件命名
- Vue组件：PascalCase（如`UserProfile.vue`）
- JavaScript文件：camelCase（如`userApi.js`）
- 样式文件：kebab-case（如`user-profile.css`）
- 配置文件：kebab-case（如`vite.config.js`）

### 目录命名
- 使用kebab-case：`user-profile/`
- 复数形式：`components/`, `views/`, `stores/`
- 语义化命名：`auth/`, `betting/`, `message/`

### 变量命名
```javascript
// 常量：UPPER_SNAKE_CASE
const API_BASE_URL = 'http://api.example.com'
const MAX_RETRY_COUNT = 3

// 变量和函数：camelCase
const userName = 'john'
const getUserInfo = () => {}

// 组件名：PascalCase
const UserProfile = defineComponent({})

// CSS类名：kebab-case
.user-profile {}
.user-profile__header {}
.user-profile--active {}
```

## API文件组织
```
src/api/
├── http.js               # HTTP配置和拦截器
├── index.js              # 统一导出所有API
├── user.js               # 用户相关API
├── chat.js               # 聊天相关API
├── betting.js            # 投注相关API
├── payment.js            # 支付相关API
├── lottery.js            # 彩票相关API
├── football.js           # 足球相关API
├── basketball.js         # 篮球相关API
└── websocket.js          # WebSocket相关
```

## 组件文件组织
```
src/components/
├── ui/                   # 基础UI组件
│   ├── BaseButton.vue
│   ├── BaseInput.vue
│   ├── BaseCard.vue
│   ├── Modal.vue
│   └── index.js          # 统一导出
├── common/               # 通用业务组件
│   ├── LoadingState.vue
│   ├── ErrorBoundary.vue
│   └── Toast.vue
├── betting/              # 投注相关组件
│   ├── football/
│   ├── basketball/
│   └── dlt/
├── message/              # 消息相关组件
│   ├── ChatItem.vue
│   ├── MessageItem.vue
│   └── NoticeItem.vue
├── user/                 # 用户相关组件
│   ├── WalletCard.vue
│   └── ServiceMenuItem.vue
└── index.js              # 全局组件注册
```

## 页面文件组织
```
src/views/
├── auth/                 # 认证页面
│   ├── LoginView.vue
│   └── RegisterView.vue
├── user/                 # 用户页面
│   ├── UserProfileView.vue
│   ├── SettingsView.vue
│   └── WalletView.vue
├── betting/              # 投注页面
│   ├── FootballBettingView.vue
│   ├── BasketballBettingView.vue
│   └── DLTBettingView.vue
├── message/              # 消息页面
│   ├── MessagesView.vue
│   ├── ChatDetailView.vue
│   └── NoticesView.vue
├── lottery/              # 彩票页面
│   ├── LotteryView.vue
│   └── DrawResultView.vue
└── home/                 # 首页
    └── HomeView.vue
```

## Store文件组织
```
src/stores/
├── index.js              # 统一导出
├── user/                 # 用户状态
│   ├── index.js
│   └── user.js
├── chat/                 # 聊天状态
│   ├── index.js
│   └── chat.js
├── websocket/            # WebSocket状态
│   ├── index.js
│   └── websocket.js
└── lottery/              # 彩票状态
    ├── index.js
    └── lottery.js
```

## 工具函数组织
```
src/utils/
├── index.js              # 统一导出
├── constants.js          # 常量定义
├── date.js               # 日期处理
├── format.js             # 格式化工具
├── validation.js         # 验证工具
├── storage.js            # 存储工具
├── websocket.js          # WebSocket工具
└── bettingCalculator.js  # 投注计算工具
```

## 测试文件组织
```
tests/
├── unit/                 # 单元测试
│   ├── components/
│   ├── utils/
│   └── stores/
├── e2e/                  # 端到端测试
│   ├── auth/
│   ├── betting/
│   └── user/
├── api/                  # API测试
└── fixtures/             # 测试数据
```

## 文档组织
```
doc/
├── README.md             # 项目概述
├── API.md                # API文档
├── DEPLOYMENT.md         # 部署文档
├── CHANGELOG.md          # 更新日志
└── modules/              # 模块文档
    ├── chat.md
    ├── betting.md
    └── payment.md
```

## 导入路径规范
```javascript
// 使用绝对路径（推荐）
import UserApi from '@/api/user'
import { useUserStore } from '@/stores/user'
import BaseButton from '@/components/ui/BaseButton.vue'

// 相对路径（仅在同级或子级目录中使用）
import './style.css'
import '../components/LocalComponent.vue'

// 第三方库
import { ref, computed } from 'vue'
import { showToast } from 'vant'
```

## 文件大小控制
- 单个Vue组件文件：< 500行
- 单个JavaScript文件：< 300行
- 单个CSS文件：< 200行
- 超过限制时应拆分为多个文件

## 代码注释规范
```javascript
/**
 * 用户相关API接口
 * <AUTHOR>
 * @since 2024-01-01
 */

/**
 * 用户登录
 * @param {Object} data 登录信息
 * @param {string} data.username 用户名
 * @param {string} data.password 密码
 * @returns {Promise<Object>} 登录结果
 */
const login = (data) => {
  return http.post('/user/login', data)
}
```
