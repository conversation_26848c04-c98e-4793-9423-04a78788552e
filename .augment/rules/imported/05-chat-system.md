---
type: "always_apply"
---

# Chat System

## Architecture

- Model files:
  - `/addons/lottery/model/ChatConversation.php` - Conversations
  - `/addons/lottery/model/ChatConversationMember.php` - Members

- Controller:
  - `/addons/lottery/command/Chat.php` - Chat command handler

- Config:
  - `/addons/lottery/config/chat.php` - Chat configuration

## Database

- Schema in `/addons/lottery/sql/chat_tables.sql`

## API

- REST API documented in `/doc/聊天系统REST接口文档.md`
- Test cases in `/tests/api/chat_api_test.php`
- Message testing in `/tests/api/get_messages_test.php`

## WebSocket

- WebSocket implementation in `/addons/lottery/library/WebSocket.php`
- Used for real-time messaging

## Conventions

- All chat-related tables have `chat_` prefix
- Conversations can have multiple members
- Authentication required for most chat operations
