---
type: "always_apply"
---

# 订单结构

## 订单表结构

项目中的订单分为主订单和子订单两部分：

1. **主订单表**：`sys_lottery_order`
   - 存储订单基本信息：用户ID、订单号、总金额、状态等

2. **子订单表**：根据彩票类型不同，有不同的子订单表
   - `sys_lottery_order_football`: 足球竞彩订单
   - `sys_lottery_order_dlt`: 大乐透订单
   - `sys_lottery_order_pls`: 排列三订单
   - `sys_lottery_order_plw`: 排列五订单
   - `sys_lottery_order_qxc`: 七星彩订单
   - `sys_lottery_order_4jq`: 四场进球订单
   - `sys_lottery_order_6jq`: 六场进球订单
   - `sys_lottery_order_sfc`: 胜负彩订单

## 订单创建流程

订单创建使用 `OrderCreatorTrait` 中的方法：

```php
trait OrderCreatorTrait
{
    // 各种彩票订单创建方法
    protected function createFootballOrder($order_id, $params)
    protected function createDltOrder($order_id, $params)
    protected function createPlsOrder($order_id, $params)
    // ...其他订单创建方法
    
    // 计算注数和金额
    protected function calculateBetCountAndAmount($lottery_type, $params)
}
```

创建订单的基本流程：

1. 接收前端参数
2. 验证必要参数
3. 创建主订单
4. 根据彩票类型创建对应子订单
5. 计算注数和总金额
6. 更新主订单总金额
7. 提交事务

## 注数和金额计算

所有订单的注数和金额计算逻辑：

1. 根据彩票类型调用对应的注数计算方法
2. 根据注数、倍数和单注价格计算总金额
3. 验证计算的金额与前端传入金额是否一致（如果有）
4. 将计算结果返回给主订单
