---
type: "always_apply"
---

# 代码风格规范

## 模型和控制器职责划分

### 业务逻辑
- 所有业务逻辑和数据操作应当封装在 Model 层中
- 控制器只负责参数获取、简单验证和调用模型方法
- 示例参考：[addons/lottery/model/Order.php](mdc:addons/lottery/model/Order.php) 中的 `pay()` 方法

### 错误处理
- 使用自定义异常类 [addons/lottery/library/Exception.php](mdc:addons/lottery/library/Exception.php)
- 模型中发现错误时，直接使用 `new Exception('错误消息')`，不需要 `throw` 关键字
- 示例：`if (!$result) { new Exception('扣减余额失败'); }`

### 控制器响应
- 控制器中使用 `$this->success()` 和 `$this->error()` 时不需要添加 `return` 关键字
- 错误：`return $this->success('操作成功');`
- 正确：`$this->success('操作成功');`

- 控制器里不需要try catch 

## 事务处理
- 事务处理逻辑应当在模型层中完成
- 使用 `Db::startTrans()`, `Db::commit()`, `Db::rollback()` 进行事务控制
- 参考 Order 模型中的 `pay()` 方法实现

## 代码注释
- 所有公共方法必须添加 PHPDoc 风格的注释
- 包含方法描述、参数说明(@param)和返回值说明(@return)


# 控制器代码规范

## 错误处理规范

### 避免在控制器中使用try-catch

在本项目中，控制器应该保持简洁，只负责：
1. 接收请求参数
2. 调用模型进行业务逻辑处理
3. 返回响应结果

错误处理应该放在模型层进行，而不是在控制器中使用try-catch。这样可以：
- 保持控制器代码简洁
- 提高代码复用性
- 分离关注点，使控制器只关注请求和响应

### 正确示例

控制器代码:
```php
public function resetPassword()
{
    $user_id = $this->request->post('user_id');
    
    if (!$user_id) {
        $this->error('请指定用户ID');
    }
    
    $result = UserModel::resetUserPassword($user_id, $this->store_id);
    
    if ($result['code'] === 1) {
        $this->success('密码重置成功', ['password' => $result['password']]);
    } else {
        $this->error($result['msg']);
    }
}
```

模型代码:
```php
public static function resetUserPassword($userId, $storeId)
{
    // 检查用户是否存在
    $user = CommonUserModel::get($userId);
    if (!$user) {
        return ['code' => 0, 'msg' => '用户不存在'];
    }
    
    // 检查用户是否属于当前店铺
    $storeUser = self::getUserDetail($userId);
    if (!$storeUser || $storeUser['store_id'] != $storeId) {
        return ['code' => 0, 'msg' => '用户不属于您的店铺'];
    }
    
    // 生成随机6位密码
    $newPassword = mt_rand(100000, 999999);
    
    try {
        // 更新用户密码
        $salt = $user->salt;
        $user->password = $user->getEncryptPassword($newPassword, $salt);
        $user->save();
        
        return ['code' => 1, 'msg' => '密码重置成功', 'password' => $newPassword];
    } catch (\Exception $e) {
        return ['code' => 0, 'msg' => '密码重置失败: ' . $e->getMessage()];
    }
}
```

### 不良示例

```php
public function resetPassword()
{
    $user_id = $this->request->post('user_id');
    
    if (!$user_id) {
        $this->error('请指定用户ID');
    }
    
    // 检查用户是否存在
    $user = CommonUserModel::get($user_id);
    if (!$user) {
        $this->error('用户不存在');
    }
    
    // 检查用户是否属于当前店铺
    $storeUser = UserModel::getUserDetail($user_id);
    if (!$storeUser || $storeUser['store_id'] != $this->store_id) {
        $this->error('用户不属于您的店铺');
    }
    
    // 生成随机6位密码
    $newPassword = mt_rand(100000, 999999);
    
    try {
        // 更新用户密码
        $salt = $user->salt;
        $user->password = $user->getEncryptPassword($newPassword, $salt);
        $user->save();
        
        $this->success('密码重置成功', ['password' => $newPassword]);
    } catch (\Exception $e) {
        $this->error('密码重置失败: ' . $e->getMessage());
    }
}
```
```
