---
type: "always_apply"
---

# 命名规范

本项目遵循以下命名规范：

## 变量命名

所有变量必须使用下划线命名法（snake_case）。例如：

```php
$user_id = 1;
$total_amount = 200;
$bet_count = 5;
$order_no = "202405091234";
```

## 函数命名

所有函数和方法必须使用小驼峰命名法（camelCase）。例如：

```php
public function createOrder()
public function calculateBetCount()
public function generateOrderNo()
```

## 类命名

类名使用大驼峰命名法（PascalCase）。例如：

```php
class OrderController
class OrderFootball
class Exception
```

## 常量命名

常量使用全大写加下划线。例如：

```php
const MAX_BET_COUNT = 100;
const DEFAULT_PRICE = 200;
```

遵循这些命名规范可以提高代码的可读性和一致性。
