---
type: "always_apply"
---

# Lottery Module

The lottery module is the core functionality of the system.

## Architecture

- `/addons/lottery/controller/` - Controllers for lottery functionality
  - `/store/` - Store-specific controllers
  - Base controllers provide common functionality

- `/addons/lottery/model/` - Data models
  - Models manage database interactions
  - Store-specific models in `/store/` subdirectory

- `/addons/lottery/library/` - Helper classes and utilities
  - `/draw/` - Draw-related functionality
  - `/order/` - Order processing
  - `/spider/` - Data scraping tools
  - `/py/` - Python integrations

- `/addons/lottery/config/` - Configuration files
  - `LotteryTypes.php` - Lottery type definitions
  - `chat.php` - Chat system configuration

## Key Files

- `/addons/lottery/Lottery.php` - Main module class
- `/addons/lottery/info.ini` - Module information

## Database

SQL schema files are located in:
- `/addons/lottery/sql/` - Schema definitions and migrations
- `/addons/lottery/data/` - SQL data scripts
