---
type: "always_apply"
---

# MVC Pattern Usage

This project follows the Model-View-Controller (MVC) pattern.

## Model

- Located in `/application/*/model/` and `/addons/*/model/`
- Handles data access and business logic
- Extends base model classes for common functionality
- Store-specific models are in `/*/model/store/` directories

## View

- Located in `/application/*/view/` directories
- Template files use HTML with ThinkPHP template syntax
- Layout files define page structure in `/*/view/layout/`
- Shared view components in `/*/view/common/`

## Controller

- Located in `/application/*/controller/` and `/addons/*/controller/`
- Handles request routing and response generation
- Controllers typically extend base controller classes
- Uses dependency injection for models and services

## Flow

1. Request enters through `/public/index.php`
2. Router maps URL to controller action
3. Controller processes request with models
4. Controller renders view or returns JSON/data
5. Response sent to client
