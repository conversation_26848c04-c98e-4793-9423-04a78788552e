---
type: "always_apply"
---

# Project Structure

This is a lottery administration system built with ThinkPHP framework.

## Main Directories

- `/application` - Core application code (MVC structure)
  - `/admin` - Admin interface controllers, models, views
  - `/api` - API controllers and services
  - `/common` - Shared components and libraries
  - `/index` - Frontend controllers and views
  
- `/addons` - Plugin modules
  - `/lottery` - Main lottery functionality
  - `/nessms` - Messaging system

- `/public` - Publicly accessible files
  - `/assets` - CSS, JavaScript, images
  - `/uploads` - User uploaded files

- `/thinkphp` - ThinkPHP framework core

- `/extend` - Framework extensions

- `/tests` - Test cases and utilities

## Entry Points

- `/public/index.php` - Main application entry
- `/public/ht.php` - Alternative entry point

## Configuration

Configuration files are found in:
- `/application/*/config.php`
- `/addons/*/config.php`
- `/application/extra/*.php`
