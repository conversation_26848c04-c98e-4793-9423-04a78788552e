---
type: "always_apply"
---

# Admin Interface

## Structure

- Controllers in `/application/admin/controller/`
  - Authentication in `/controller/auth/`
  - User management in `/controller/user/`
  - General settings in `/controller/general/`

- Models in `/application/admin/model/`
  - `Admin.php` - Admin users
  - `AdminLog.php` - Admin activity logs
  - `AuthGroup.php` - Permission groups

- Views in `/application/admin/view/`
  - Layout templates in `/view/layout/`
  - Shared components in `/view/common/`

## Authentication

- Authentication handled by `/application/admin/library/Auth.php`
- Permission checking with auth groups and rules
- Admin logs track all admin activities

## Features

- CRUD operations with `/application/admin/command/Crud.php`
- Addon management with `/application/admin/command/Addon.php`
- API generation with `/application/admin/command/Api.php`

## Assets

Frontend assets for admin interface:
- CSS: `/public/assets/css/backend.css`
- JS: `/public/assets/js/backend/`
- Libraries: `/public/assets/libs/`
