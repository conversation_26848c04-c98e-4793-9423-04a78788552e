---
type: "always_apply"
---

# UI和样式规范

## 基本原则
- 优先使用Vant UI组件
- 使用Tailwind CSS进行样式定制
- 保持设计一致性和用户体验
- 响应式设计，适配移动端

## 技术栈
- UI库：Vant 4.x
- CSS框架：Tailwind CSS
- 图标：Vant内置图标
- 字体：Inter, sans-serif

## 主题色彩配置
```javascript
// tailwind.config.js
colors: {
  primary: '#EF4444',    // 主色调（红色）
  secondary: '#F59E0B',  // 次要色（橙色）
  success: '#10B981',    // 成功色（绿色）
  danger: '#EF4444',     // 危险色（红色）
  warning: '#F59E0B',    // 警告色（橙色）
  info: '#3B82F6',       // 信息色（蓝色）
  light: '#F3F4F6',      // 浅色
  dark: '#1F2937',       // 深色
}
```

## Vant组件使用规范
```vue
<template>
  <!-- 按钮 -->
  <van-button type="primary" size="large" @click="handleClick">
    主要按钮
  </van-button>

  <!-- 输入框 -->
  <van-field
    v-model="value"
    label="标签"
    placeholder="请输入内容"
    :rules="[{ required: true, message: '请输入内容' }]"
  />

  <!-- 弹窗 -->
  <van-popup v-model:show="showPopup" position="bottom">
    弹窗内容
  </van-popup>

  <!-- 列表 -->
  <van-list
    v-model:loading="loading"
    :finished="finished"
    @load="onLoad"
  >
    <van-cell v-for="item in list" :key="item.id" :title="item.title" />
  </van-list>

  <!-- 图片预览 -->
  <van-image-preview
    v-model:show="showPreview"
    :images="images"
    :start-position="startPosition"
  />
</template>
```

## Tailwind CSS使用规范
```vue
<template>
  <!-- 布局 -->
  <div class="flex flex-col min-h-screen bg-gray-50">
    <!-- 头部 -->
    <header class="bg-white shadow-sm px-4 py-3">
      <h1 class="text-lg font-semibold text-gray-900">标题</h1>
    </header>

    <!-- 主内容 -->
    <main class="flex-1 p-4">
      <!-- 卡片 -->
      <div class="bg-white rounded-lg shadow-sm p-4 mb-4">
        <h2 class="text-base font-medium text-gray-900 mb-2">卡片标题</h2>
        <p class="text-sm text-gray-600">卡片内容</p>
      </div>
    </main>

    <!-- 底部导航 -->
    <nav class="bg-white border-t border-gray-200">
      <!-- 导航内容 -->
    </nav>
  </div>
</template>
```

## 响应式设计
```css
/* 移动端优先 */
.container {
  @apply px-4;
}

/* 平板 */
@screen md {
  .container {
    @apply px-6;
  }
}

/* 桌面端 */
@screen lg {
  .container {
    @apply px-8;
  }
}
```

## 组件样式规范
```vue
<style scoped>
/* 使用BEM命名规范 */
.lottery-card {
  @apply bg-white rounded-lg shadow-sm p-4;
}

.lottery-card__header {
  @apply flex items-center justify-between mb-3;
}

.lottery-card__title {
  @apply text-lg font-semibold text-gray-900;
}

.lottery-card__content {
  @apply text-sm text-gray-600;
}

.lottery-card--active {
  @apply border-2 border-primary;
}
</style>
```

## 常用样式模式
```css
/* 卡片样式 */
.card {
  @apply bg-white rounded-lg shadow-sm p-4;
}

/* 按钮样式 */
.btn-primary {
  @apply bg-primary text-white px-4 py-2 rounded-md font-medium;
}

/* 输入框样式 */
.input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary;
}

/* 文本样式 */
.text-title {
  @apply text-lg font-semibold text-gray-900;
}

.text-subtitle {
  @apply text-base font-medium text-gray-700;
}

.text-body {
  @apply text-sm text-gray-600;
}

.text-caption {
  @apply text-xs text-gray-500;
}
```

## 图片处理规范
```vue
<template>
  <!-- 头像 -->
  <van-image
    :src="avatarUrl"
    round
    width="40"
    height="40"
    :error-icon="'user-o'"
    :loading-icon="'photo-o'"
  />

  <!-- 图片列表 -->
  <div class="grid grid-cols-3 gap-2">
    <van-image
      v-for="(image, index) in images"
      :key="index"
      :src="image"
      fit="cover"
      width="100"
      height="100"
      @click="previewImage(index)"
    />
  </div>

  <!-- 图片上传 -->
  <van-uploader
    v-model="fileList"
    :max-count="9"
    :after-read="afterRead"
    :before-read="beforeRead"
  />
</template>
```

## 动画和过渡
```vue
<template>
  <!-- 页面过渡 -->
  <transition name="slide-left">
    <div v-if="show" class="page">
      页面内容
    </div>
  </transition>

  <!-- 列表项动画 -->
  <transition-group name="list" tag="div">
    <div v-for="item in list" :key="item.id" class="list-item">
      {{ item.name }}
    </div>
  </transition-group>
</template>

<style scoped>
/* 滑动动画 */
.slide-left-enter-active,
.slide-left-leave-active {
  transition: transform 0.3s ease;
}

.slide-left-enter-from {
  transform: translateX(100%);
}

.slide-left-leave-to {
  transform: translateX(-100%);
}

/* 列表动画 */
.list-enter-active,
.list-leave-active {
  transition: all 0.3s ease;
}

.list-enter-from,
.list-leave-to {
  opacity: 0;
  transform: translateY(20px);
}
</style>
```

## 深色模式支持
```css
/* 使用CSS变量支持主题切换 */
:root {
  --bg-primary: #ffffff;
  --text-primary: #1f2937;
}

[data-theme="dark"] {
  --bg-primary: #1f2937;
  --text-primary: #ffffff;
}

.themed-element {
  background-color: var(--bg-primary);
  color: var(--text-primary);
}
```
