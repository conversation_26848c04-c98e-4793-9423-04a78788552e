---
type: "always_apply"
---

# API开发规范

## 基本原则
- 所有API调用必须通过统一的http实例进行
- API接口按功能模块组织，每个模块一个文件
- 使用JSDoc注释详细描述接口参数和返回值
- 不在控制器中使用try-catch语句进行错误处理（由http拦截器统一处理）

## API文件结构
```javascript
import http from './http'

/**
 * [模块名]相关API接口
 */
const [模块名]Api = {
  /**
   * [接口描述]
   * @param {Object} data 请求参数
   * @param {String} data.param1 参数1描述
   * @param {String} [data.param2] 可选参数2描述
   * @returns {Promise}
   */
  methodName(data) {
    return http.post('/api/path', data)
  }
}

export default [模块名]Api
```

## 接口调用规范
- 基础URL配置：`http://www.lottery.com:8012/addons/lottery/`
- 认证方式：Bearer Token + token头部字段（双重兼容）
- 请求超时：15秒
- 自动重试：网络错误时最多重试2次

## 错误处理
- 业务成功状态码：[0, 1, 200]
- 认证错误码：[401, 10001, 10002]
- 权限错误码：403
- 错误处理由http拦截器统一处理，组件中无需额外处理

## 特殊接口规范
### 文件上传
```javascript
// 文件上传统一使用此接口
const uploadUrl = 'http://www.lottery.com:8012/addons/lottery/index/upload'
// 返回数据的data.fullurl字段包含上传后的文件URL
```

### 聊天相关接口
```javascript
// 聊天接口路径使用 /chat/ 而不是 /store.chat/
// baseUrl已包含/addons/lottery前缀，避免重复
```

## API白名单
以下API不需要token验证：
- `/user/login`
- `/user/register` 
- `/user/mobilelogin`
- `/sms/send`
- `/lottery/list`
- `/common/`开头的所有接口

## 请求拦截器功能
- 自动添加Authorization头部
- 防重复请求机制
- 未登录状态自动跳转登录页
- 页面白名单检查

## 响应拦截器功能
- 统一业务状态码处理
- 自动token过期处理
- 网络错误重试机制
- 统一错误提示显示
