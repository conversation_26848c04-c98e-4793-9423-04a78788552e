{"name": "bootstrap-select", "description": "The jQuery plugin that brings select elements into the 21st century with intuitive multiselection, searching, and much more.", "main": ["./less/bootstrap-select.less", "./dist/css/bootstrap-select.css", "./dist/js/bootstrap-select.js"], "homepage": "https://developer.snapappointments.com/bootstrap-select", "authors": [{"name": "<PERSON>", "homepage": "https://github.com/caseyjhol"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://github.com/silviomoreto"}, {"name": "SnapAppointments, LLC", "homepage": "https://snapappointments.com"}], "repository": {"type": "git", "url": "git://github.com/snapappointments/bootstrap-select.git"}, "keywords": ["javascript", "j<PERSON>y", "form", "bootstrap", "dropdown", "select", "replacement"], "dependencies": {"jquery": "1.9.1 - 3", "bootstrap": ">=3.0.0"}, "license": "MIT", "ignore": [".es<PERSON><PERSON><PERSON>", ".giti<PERSON>re", "CONTRIBUTING.md", "Gruntfile.js", "composer.json", ".github", "docs", "js", "!dist/js", "nuget", "tests"], "version": "1.13.18", "_release": "1.13.18", "_resolution": {"type": "version", "tag": "v1.13.18", "commit": "6c4c75f61e69ca54d5ab557a2b90ffdccf1c63cd"}, "_source": "https://github.com/snapappointments/bootstrap-select.git", "_target": "~1.13.18", "_originalSource": "bootstrap-select"}