{"name": "bootstrap-select", "description": "The jQuery plugin that brings select elements into the 21st century with intuitive multiselection, searching, and much more.", "main": ["./less/bootstrap-select.less", "./dist/css/bootstrap-select.css", "./dist/js/bootstrap-select.js"], "homepage": "https://developer.snapappointments.com/bootstrap-select", "authors": [{"name": "<PERSON>", "homepage": "https://github.com/caseyjhol"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://github.com/silviomoreto"}, {"name": "SnapAppointments, LLC", "homepage": "https://snapappointments.com"}], "repository": {"type": "git", "url": "git://github.com/snapappointments/bootstrap-select.git"}, "keywords": ["javascript", "j<PERSON>y", "form", "bootstrap", "dropdown", "select", "replacement"], "dependencies": {"jquery": "1.9.1 - 3", "bootstrap": ">=3.0.0"}, "license": "MIT", "ignore": [".es<PERSON><PERSON><PERSON>", ".giti<PERSON>re", "CONTRIBUTING.md", "Gruntfile.js", "composer.json", ".github", "docs", "js", "!dist/js", "nuget", "tests"]}