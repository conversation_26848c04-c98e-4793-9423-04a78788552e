{"name": "bootstrap-select", "title": "bootstrap-select", "main": "dist/js/bootstrap-select.js", "description": "The jQuery plugin that brings select elements into the 21st century with intuitive multiselection, searching, and much more. Now with Bootstrap 4 support.", "version": "1.13.18", "homepage": "https://developer.snapappointments.com/bootstrap-select", "author": {"name": "<PERSON>", "url": "https://github.com/caseyjhol"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/silviomoreto"}, {"name": "SnapAppointments, LLC", "url": "https://snapappointments.com"}], "repository": {"type": "git", "url": "git://github.com/snapappointments/bootstrap-select.git"}, "license": "MIT", "dependencies": {}, "peerDependencies": {"jquery": "1.9.1 - 3", "bootstrap": ">=3.0.0"}, "devDependencies": {"autoprefixer": "^9.7.6", "grunt": "^1.1.0", "grunt-banner": "^0.6.0", "grunt-contrib-clean": "^2.0.0", "grunt-contrib-compress": "^1.6.0", "grunt-contrib-concat": "^1.0.1", "grunt-contrib-copy": "^1.0.0", "grunt-contrib-csslint": "^2.0.0", "grunt-contrib-cssmin": "^3.0.0", "grunt-contrib-less": "^2.0.0", "grunt-contrib-uglify": "^4.0.1", "grunt-contrib-watch": "^1.0.0", "grunt-eslint": "^22.0.0", "grunt-postcss": "^0.9.0", "grunt-version": "^2.0.0", "load-grunt-tasks": "^5.1.0"}, "browserslist": ["Android 2.3", "Android >= 4", "Chrome >= 20", "Firefox >= 24", "Explorer >= 8", "iOS >= 6", "Opera >= 12", "Safari >= 6"], "keywords": ["javascript", "j<PERSON>y", "form", "bootstrap", "dropdown", "select", "replacement"]}