{"name": "fastadmin-citypicker", "description": "A simple jQuery plugin for picking provinces, cities and districts of China.", "version": "1.3.6", "main": "dist/city-picker.js", "license": "MIT", "repository": "karsonzhang/fastadmin-citypicker", "homepage": "http://tshi0912.github.io/city-picker", "author": {"name": "Tao Shi", "url": "http://shitao.me"}, "keywords": ["中国", "省份", "城市", "行政区", "省市区", "三级联动", "地址选择器", "China", "province", "provinces", "city", "cities", "district", "districts", "pick", "picker", "picking", "j<PERSON>y", "plugin", "html", "css", "javascript", "front-end", "web", "development"], "dependencies": {"jquery": ">= 1.9.1"}, "devDependencies": {"gulp": "^3.9.0", "gulp-jscs": "^3.0.2", "gulp-jshint": "^1.12.0", "gulp-load-plugins": "^1.1.0", "gulp-rename": "^1.2.2", "gulp-replace": "^0.5.4", "gulp-uglify": "^1.5.1"}}