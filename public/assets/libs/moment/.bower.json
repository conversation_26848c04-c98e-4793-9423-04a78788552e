{"name": "moment", "license": "MIT", "main": "moment.js", "ignore": ["**/.*", "benchmarks", "bower_components", "meteor", "node_modules", "scripts", "tasks", "test", "component.json", "composer.json", "CONTRIBUTING.md", "ender.js", "Gruntfile.js", "Moment.js.nuspec", "package.js", "package.json", "ISSUE_TEMPLATE.md", "typing-tests", "min/tests.js"], "homepage": "https://github.com/moment/moment", "version": "2.29.4", "_release": "2.29.4", "_resolution": {"type": "version", "tag": "2.29.4", "commit": "7278f421be38466bb6ca323d652b4a5759146ba1"}, "_source": "https://github.com/moment/moment.git", "_target": "~2.29.0", "_originalSource": "moment"}