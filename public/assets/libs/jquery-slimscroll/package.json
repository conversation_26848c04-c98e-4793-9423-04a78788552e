{"name": "j<PERSON>y-slimscroll", "main": "jquery.slimscroll.js", "version": "1.3.8", "title": "jQ<PERSON>y slimScroll scrollbar", "description": "slimScroll is a small jQuery plugin that transforms any div into a scrollable area. slimScroll doesn't occupy any visual space as it only appears on a user initiated mouse-over.", "keywords": ["scrollbar", "scroll", "slimscroll", "scrollable", "scrolling", "scroller", "ui", "jquery-plugin", "ecosystem:jquery"], "homepage": "http://rocha.la/jQuery-slimScroll/", "repository": {"type": "git", "url": "https://github.com/rochal/jQuery-slimScroll.git"}, "author": {"name": "<PERSON><PERSON><PERSON>", "url": "http://rocha.la/"}, "dependencies": {"jquery": ">= 1.7"}, "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/mit-license.php"}, {"type": "GPL", "url": "http://www.opensource.org/licenses/gpl-license.php"}]}