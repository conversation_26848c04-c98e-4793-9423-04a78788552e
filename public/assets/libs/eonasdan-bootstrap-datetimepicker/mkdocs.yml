site_name: Bootstrap 3 Datepicker
docs_dir: src/docs
site_dir: docs
theme:
    name: null
    custom_dir: src/docs/theme
extra_javascript: ['//cdnjs.cloudflare.com/ajax/libs/moment.js/2.9.0/moment-with-locales.js','//cdn.rawgit.com/Eonasdan/bootstrap-datetimepicker/e8bddc60e73c1ec2475f827be36e1957af72e2ea/src/js/bootstrap-datetimepicker.js']
extra_css: ['//cdn.rawgit.com/Eonasdan/bootstrap-datetimepicker/e8bddc60e73c1ec2475f827be36e1957af72e2ea/build/css/bootstrap-datetimepicker.css']
repo_url: https://github.com/Eonasdan/bootstrap-datetimepicker
nav:
- Usage: 'index.md'
- Installing: 'Installing.md'
- Functions: 'Functions.md'
- Options: 'Options.md'
- Events: 'Events.md'
- Change Log: 'Changelog.md'
- Dev Guide: 'ContributorsGuide.md'
- Extras: 'Extras.md'
- FAQs: 'FAQ.md'