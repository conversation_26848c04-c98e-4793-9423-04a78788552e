{"author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/eonasdan/bootstrap-datetimepicker/issues"}, "peerDependencies": {"bootstrap": "^3.3", "jquery": "^1.8.3 || ^2.0 || ^3.0", "moment": "^2.10", "moment-timezone": "^0.4.0 || ^0.5.0"}, "dependencies": {"bootstrap": "^3.3", "jquery": "^3.5.1", "moment": "^2.10", "moment-timezone": "^0.4.0"}, "description": "A date/time picker component designed to work with Bootstrap 3 and Momentjs. For usage, installation and demos see Project Site on GitHub", "devDependencies": {"grunt": "^1.3.0", "grunt-contrib-connect": "^1.0.1", "grunt-contrib-jasmine": "^2.2.0", "grunt-contrib-jshint": "latest", "grunt-contrib-less": "latest", "grunt-contrib-uglify": "latest", "grunt-env": "^1.0.1", "grunt-nuget": "^0.3.1", "grunt-string-replace": "latest", "load-grunt-tasks": "latest"}, "homepage": "http://eonasdan.github.io/bootstrap-datetimepicker/", "keywords": ["twitter-bootstrap", "bootstrap", "datepicker", "datetimepicker", "timepicker", "moment"], "license": "MIT", "main": "src/js/bootstrap-datetimepicker.js", "name": "e<PERSON><PERSON><PERSON>-bootstrap-datetimepicker", "repository": {"type": "git", "url": "https://github.com/eonasdan/bootstrap-datetimepicker.git"}, "version": "4.17.49"}