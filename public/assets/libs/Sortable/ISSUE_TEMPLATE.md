#### Problem:



#### JSBin/JSFiddle demonstrating the problem:


---
Before you create an issue, check it:

 1. Try [master](https://github.com/SortableJS/Sortable/tree/master/)-branch, perhaps the problem has been solved;
 2. [Use the search](https://github.com/SortableJS/Sortable/search?q=problem), maybe we already have an answer;
 3. If not found, create an example on [jsbin.com (draft)](http://jsbin.com/vojixek/edit?html,js,output) and describe the problem.

Bindings:
 - Angular
   - 2.0+: https://github.com/SortableJS/angular-sortablejs/issues
   - legacy: https://github.com/SortableJS/angular-legacy-sortablejs/issues
 - React
   - ES2015+: https://github.com/SortableJS/react-sortablejs/issues
   - mixin: https://github.com/SortableJS/react-mixin-sortablejs/issues
 - Polymer: https://github.com/SortableJS/polymer-sortablejs/issues
 - Knockout: https://github.com/SortableJS/knockout-sortablejs/issues
 - Meteor: https://github.com/SortableJS/meteor-sortablejs/issues
