{"name": "Sortable", "main": ["Sortable.js"], "homepage": "http://SortableJS.github.io/Sortable/", "authors": ["RubaXa <<EMAIL>>", "owenm <<EMAIL>>"], "description": "JavaScript library for reorderable drag-and-drop lists on modern browsers and touch devices. No jQuery required. Supports Meteor, AngularJS, React, Polymer, Vue, Knockout and any CSS library, e.g. Bootstrap.", "keywords": ["sortable", "reorder", "list", "html5", "drag", "and", "drop", "dnd", "web-components"], "license": "MIT", "ignore": ["node_modules", "bower_components", "test", "tests"]}