{"name": "nice-validator", "description": "Simple, smart and pleasant verification solution.", "moduleType": ["amd", "node", "globals"], "keywords": ["j<PERSON>y", "validation", "form", "validator", "validate", "nice-validator", "jquery-plugin"], "authors": ["<PERSON><PERSON> <<EMAIL>>"], "license": "MIT", "homepage": "https://github.com/niceue/nice-validator", "repository": {"type": "git", "url": "https://github.com/niceue/nice-validator.git"}, "main": "dist/jquery.validator.js", "dependencies": {"jquery": ">=1.7"}, "ignore": [".*", "src", "test", "gulpfile.js", "*.json", "*.md", "*.yml", "!README.md"], "version": "1.1.6", "_release": "1.1.6", "_resolution": {"type": "version", "tag": "1.1.6", "commit": "583061e6af8e2309194790884ebf9618453ad0cb"}, "_source": "https://github.com/karsonzhang/fastadmin-nicevalidator.git", "_target": "~1.1.6", "_originalSource": "karsonzhang/fastadmin-nicevalidator"}