{"name": "fastadmin-bootstraptable", "description": "An extended Bootstrap table with radio, checkbox, sort, pagination, and other added features. (supports twitter bootstrap v2 and v3).", "version": "1.11.12", "main": "./dist/bootstrap-table.js", "devDependencies": {"cz-conventional-changelog": "^1.1.5", "grunt": "^1.5.3", "grunt-contrib-clean": "^2.0.1", "grunt-contrib-concat": "^0.5.1", "grunt-contrib-copy": "^0.8.0", "grunt-contrib-cssmin": "^4.0.0", "grunt-contrib-jshint": "^3.2.0", "grunt-contrib-uglify": "^5.2.2"}, "repository": {"type": "git", "url": "https://github.com/karsonzhang/fastadmin-bootstraptable.git"}, "keywords": ["bootstrap", "table", "radio", "checkbox", "sort", "pagination", "editable"], "author": "wenzhixin <<EMAIL>> (http://wenzhixin.net.cn/)", "license": "MIT", "bugs": {"url": "https://github.com/karsonzhang/fastadmin-bootstraptable/issues"}, "homepage": "https://github.com/karsonzhang/fastadmin-bootstraptable", "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "dependencies": {"uglify-js": "^2.8.29"}}