{"name": "bootstrap-table", "homepage": "https://github.com/wenzhixin/bootstrap-table", "authors": ["zhixin <<EMAIL>>"], "description": "An extended Bootstrap table with radio, checkbox, sort, pagination, and other added features.", "main": ["src/bootstrap-table.js", "src/bootstrap-table.css"], "keywords": ["bootstrap", "table", "bootstrap table"], "license": "MIT", "ignore": ["**/.*", "node_modules", "bower_components", "test", "tests", "docs", "assets"], "version": "1.11.12", "_release": "1.11.12", "_resolution": {"type": "version", "tag": "v1.11.12", "commit": "65e30ce18b889b1bd259924e314afadc36a4ec60"}, "_source": "https://github.com/karsonzhang/fastadmin-bootstraptable.git", "_target": "~1.11.5", "_originalSource": "fastadmin-bootstraptable"}