
## 概要
layer 是一款历来都备受青睐的 Web 弹出层组件，具备全方位的解决方案，面向的是各个水平段的开发人员，您的页面会轻松地拥有丰富友好的操作体验。在与同类组件的比较中，layer 会更能被开发者所选择。这不仅是凭「脸」取胜，而是它尽可能地在以更少的代码展现更强健的功能，且格外注重性能的提升、易用和实用性，layer 甚至还兼容了包括 IE6 在内的所有主流浏览器。其数量可观的基础属性和方法，使得您可以自定义太多您需要的风格，每一种弹层模式各具特色，广受欢迎。当然，这种「王婆卖瓜」的陈述听起来总是有点难受，因此你需要进一步了解她是否真的如你所愿。

[文档与演示](http://layer.layui.com/) 

## 愿景
成为网页弹出层的首先交互方案

## 现状
因着数年的坚持维护，已被运用在不计其数 Web 平台。几乎所处可见，其中还不乏众多知名大型网站。layer 已被国内乃至全世界至少数十万的开发者所使用过。


## 相关
[官网](http://layer.layui.com/)