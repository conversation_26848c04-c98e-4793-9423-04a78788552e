{"name": "tableexport.jquery.plugin", "version": "1.10.26", "description": "html table export", "main": "tableExport.js", "authors": ["hhurz"], "license": "MIT", "keywords": ["html5", "javascript", "j<PERSON>y", "export", "table"], "homepage": "https://github.com/hhurz/tableExport.jquery.plugin", "dependencies": {"jquery": ">=1.9.1", "es6-promise": ">=4.2.4", "file-saver": ">=1.2.0", "html2canvas": "*", "jspdf": ">=1.3.4", "jspdf-autotable": "2.0.14 || 2.0.17", "pdfmake": "^0.1.71"}, "moduleType": ["globals"], "ignore": [".git", ".idea", "package.json", "package-lock.json", "libs", "node_modules", "bower_components", "test", "tools"]}