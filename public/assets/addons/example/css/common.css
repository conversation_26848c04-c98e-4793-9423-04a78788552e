html,
body {
    height: 100%;
}

body {
    padding-top: 50px; /* Required padding for .navbar-fixed-top. Remove if using .navbar-static-top. Change if height of navigation changes. */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-family: 'Source Sans Pro', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

.img-addon {
    margin-bottom: 10px;
    width:100%;
}

.img-hover:hover {
    opacity: 0.8;
}

.display-1 {
    font-size:44px;
}
.display-4 {
    font-size:24px;
    line-height:32px;
}

/* Home Page Carousel */

header.carousel {
    height: 50%;
}

header.carousel .item,
header.carousel .item.active,
header.carousel .carousel-inner {
    height: 100%;
}

header.carousel .fill {
    width: 100%;
    height: 100%;
}

/* 404 Page Styles */

.error-404 {
    font-size: 100px;
}

/* Pricing Page Styles */

.price {
    display: block;
    font-size: 50px;
    line-height: 50px;
}

.price sup {
    top: -20px;
    left: 2px;
    font-size: 20px;
}

.period {
    display: block;
    font-style: italic;
}

/* Footer Styles */

footer {
    margin: 50px 0;
}

/* Responsive Styles */

@media(max-width:991px) {
    .customer-img,
    .img-related {
        margin-bottom: 30px;
    }
}

@media(max-width:767px) {
    .img-addon {
        margin-bottom: 15px;
    }

    header.carousel .carousel {
        height: 70%;
    }
}
.carousel-body {
    position:absolute;
    width: 100%;
    top:25%;
    text-align:center;
    color:#fff;
}

.addonlist a > p{
    margin-bottom:15px;
}
