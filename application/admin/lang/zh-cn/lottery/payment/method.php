<?php

return [
    'User_id'        => '用户ID',
    'Type'           => '支付方式类型：bank-银行卡,alipay-支付宝,wechat-微信',
    'Account_name'   => '账户名称/开户人姓名',
    'Account_number' => '账号（银行卡号/支付宝账号/微信账号）',
    'Bank_name'      => '银行名称（仅银行卡使用）',
    'Qr_code_url'    => '收款码URL（仅支付宝和微信支付方式使用）',
    'Is_default'     => '是否默认,1-是,0-否',
    'Status'         => '状态：0-禁用,1-启用',
    'Createtime'     => '创建时间',
    'Updatetime'     => '更新时间',
    'Deletetime'     => '删除时间'
];
