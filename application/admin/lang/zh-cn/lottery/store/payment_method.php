<?php

return [
    'Id'             => '收款方式ID',
    'Store_id'       => '商户ID',
    'Method_name'    => '收款方式名称',
    'Method_type'    => '支付方式类型：alipay=支付宝,wechat=微信支付,bank_transfer=银行卡转账',
    'Account_name'   => '收款账户名称/持卡人姓名',
    'Account_number' => '收款账号（支付宝账号/微信号/银行卡号）',
    'Bank_name'      => '银行名称（仅银行转账时使用）',
    'Bank_branch'    => '开户行支行（仅银行转账时使用）',
    'Qr_code_url'    => '收款二维码图片URL',
    'Status'         => '状态：0=禁用,1=启用',
    'Sort_order'     => '排序权重,数字越大越靠前',
    'Remark'         => '备注说明',
    'Createtime'     => '创建时间',
    'Updatetime'     => '更新时间',
    'Deletetime'     => '删除时间'
];
