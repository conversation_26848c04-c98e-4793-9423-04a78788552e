<?php

return [
    'Order_id'     => '关联订单ID',
    'User_id'      => '用户ID',
    'Store_id'     => '商户id',
    'Issue_id'     => '期号ID',
    'Issue_number' => '期号',
    'Bet_type'     => '投注类型：zx_dwfs=定位复式,zx_zh3bt=组合三不同,zx_hz=和值,zx_zhdt=组合胆拖,zx_kdfs=跨度复式,zu3_ds=组三单式,zu3_fs=组三复式,zu3_td=组三胆拖,zu6_fs=组六复式,zu6_td=组六胆拖,zu6_kdfs=组六跨度复式,zux_hz=组选和值,zux_2qb=2码全包',
    'Bet_count'    => '注数',
    'Bet_multiple' => '倍数',
    'Bet_amount'   => '投注金额',
    'Bet_data'     => '投注号码,JSON格式存储',
    'Prize_amount' => '中奖金额',
    'Status'       => '状态：0待开奖,1已中奖,2未中奖',
    'Createtime'   => '创建时间',
    'Updatetime'   => '更新时间',
    'Deletetime'   => '删除时间',
    'Settletime'   => '结算时间'
];
