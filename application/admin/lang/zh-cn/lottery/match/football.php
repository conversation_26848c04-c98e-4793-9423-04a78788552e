<?php

return [
    'League'      => '联赛名称',
    'Match_date'  => '比赛时间',
    'Match_time'  => '比赛时间',
    'Match_num'   => '比赛编号',
    'Weekday'     => '星期',
    'Status'      => '比赛状态：1未开始,2进行中,3已结束',
    'Draw_status' => '开奖状态： 1待开奖 2已开奖',
    'Team1_id'    => '主队id',
    'Team1_name'  => '主队名称',
    'Team1_logo'  => '主队logo',
    'Team2_id'    => '客队id',
    'Team2_name'  => '客队名称',
    'Team2_logo'  => '客队logo',
    'Odds_data'   => '赔率数据,JSON格式',
    'Pool_data'   => '赔率配置, Json格式',
    'Result_data' => '比赛结果,JSON格式',
    'Sort'        => '排序',
    'Createtime'  => '创建时间',
    'Updatetime'  => '更新时间',
    'Deletetime'  => '删除时间'
];
