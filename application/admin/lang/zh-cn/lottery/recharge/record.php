<?php

return [
    'Id'                      => 'ID',
    'User_id'                 => '用户ID',
    'Username'                => '用户名',
    'Mobile'                  => '手机号',
    'User_remark'             => '用户备注',
    'Store_id'                => '店铺ID',
    'Amount'                  => '充值金额',
    'Payment_method'          => '支付方式',
    'Payment_method_id'       => '支付方式ID',
    'Store_payment_method_id' => '商户收款方式ID',
    'Status'                  => '状态',
    'Status 0'                => '待支付',
    'Set status to 0'         => '设为待支付',
    'Status 1'                => '已支付',
    'Set status to 1'         => '设为已支付',
    'Status 2'                => '已取消',
    'Set status to 2'         => '设为已取消',
    'Status 3'                => '待审核',
    'Set status to 3'         => '设为待审核',
    'Status 4'                => '审核通过',
    'Set status to 4'         => '设为审核通过',
    'Status 5'                => '审核驳回',
    'Set status to 5'         => '设为审核驳回',
    'Payment_proof'           => '支付凭证',
    'Actual_amount'           => '实际支付金额',
    'Fee_amount'              => '手续费',
    'Order_no'                => '订单号',
    'Remark'                  => '用户备注',
    'Admin_remark'            => '管理员备注',
    'Createtime'              => '申请时间',
    'Updatetime'              => '更新时间',
    'Deletetime'              => '删除时间',
    'Is_read'                 => '是否已读',
    'Is_read 0'               => '未读',
    'Is_read 1'               => '已读',
    // 支付方式
    'bank_transfer'           => '银行转账',
    'wechat'                  => '微信支付',
    'alipay'                  => '支付宝'
];
