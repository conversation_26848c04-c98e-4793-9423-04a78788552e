<form id="edit-form" class="form-horizontal form-ajax" role="form" data-toggle="validator" method="POST" action="">
    {:token()}
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Group')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_select('group[]', $groupdata, $groupids, ['class'=>'form-control selectpicker', 'multiple'=>'', 'data-rule'=>'required'])}
        </div>
    </div>
    <div class="form-group">
        <label for="username" class="control-label col-xs-12 col-sm-2">{:__('Username')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" class="form-control" id="username" name="row[username]" value="{$row.username|htmlentities}" data-rule="required;username" />
        </div>
    </div>
    <div class="form-group">
        <label for="email" class="control-label col-xs-12 col-sm-2">{:__('Email')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="email" class="form-control" id="email" name="row[email]" value="{$row.email|htmlentities}" data-rule="required;email" />
        </div>
    </div>
    <div class="form-group">
        <label for="mobile" class="control-label col-xs-12 col-sm-2">{:__('Mobile')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" class="form-control" id="mobile" name="row[mobile]" value="{$row.mobile|default=''|htmlentities}" data-rule="mobile" />
        </div>
    </div>
    <div class="form-group">
        <label for="nickname" class="control-label col-xs-12 col-sm-2">{:__('Nickname')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" class="form-control" id="nickname" name="row[nickname]" autocomplete="off" value="{$row.nickname|htmlentities}" data-rule="required" />
        </div>
    </div>
    <div class="form-group">
        <label for="password" class="control-label col-xs-12 col-sm-2">{:__('Password')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="password" class="form-control" id="password" name="row[password]" autocomplete="new-password" value="" data-rule="password" />
        </div>
    </div>
    <div class="form-group">
        <label for="loginfailure" class="control-label col-xs-12 col-sm-2">{:__('Loginfailure')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="number" class="form-control" id="loginfailure" name="row[loginfailure]" value="{$row.loginfailure|htmlentities}" data-rule="required" />
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">关联商户:</label>
        <div class="col-xs-12 col-sm-8">
            {if condition="!$is_super_admin && $current_admin_store_id > 0"}
                {:build_select('row[store_id]', $storedata, $current_admin_store_id, ['class'=>'form-control selectpicker', 'disabled'=>'disabled'])}
                <span class="help-block">当前管理员权限限制，只能编辑为同店铺的管理员</span>
            {else /}
                {:build_select('row[store_id]', $storedata, $row['store_id'], ['class'=>'form-control selectpicker'])}
            {/if}
        </div>
    </div>
    <div class="form-group">
        <label for="min_order_amount" class="control-label col-xs-12 col-sm-2">最小订单金额:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="number" step="0.01" min="0" class="form-control" id="min_order_amount" name="row[min_order_amount]" value="{$row.min_order_amount|default='0.00'}" placeholder="0表示无限制" />
        </div>
    </div>
    <div class="form-group">
        <label for="max_order_amount" class="control-label col-xs-12 col-sm-2">最大订单金额:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="number" step="0.01" min="0" class="form-control" id="max_order_amount" name="row[max_order_amount]" value="{$row.max_order_amount|default='0.00'}" placeholder="0表示无限制" />
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">允许的彩种:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_select('allowed_lottery_types[]', $lotterytypedata, explode(',', $row['allowed_lottery_types']), ['class'=>'form-control selectpicker', 'multiple'=>'', 'data-none-selected-text'=>'全部彩种'])}
            <span class="help-block">不选择任何彩种表示允许所有彩种</span>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[status]', ['normal'=>__('Normal'), 'hidden'=>__('Hidden')], $row['status'])}
        </div>
    </div>
    <div class="form-group hidden layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
