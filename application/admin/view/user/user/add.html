<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
  {:token()}

  <div class="form-group">
    <label for="c-username" class="control-label col-xs-12 col-sm-2">{:__('Username')}:</label>
    <div class="col-xs-12 col-sm-4">
      <input id="c-username" data-rule="required" class="form-control" name="row[username]" type="text" >
    </div>
  </div>
  <div class="form-group">
    <label for="c-nickname" class="control-label col-xs-12 col-sm-2">{:__('Nickname')}:</label>
    <div class="col-xs-12 col-sm-4">
      <input id="c-nickname" data-rule="required" class="form-control" name="row[nickname]" type="text" >
    </div>
  </div>
  <div class="form-group">
    <label for="c-password" class="control-label col-xs-12 col-sm-2">{:__('Password')}:</label>
    <div class="col-xs-12 col-sm-4">
      <input id="c-password" data-rule="password" class="form-control" name="row[password]" type="password"   />
    </div>
  </div>

  <div class="form-group">
    <label for="c-mobile" class="control-label col-xs-12 col-sm-2">{:__('Mobile')}:</label>
    <div class="col-xs-12 col-sm-4">
      <input id="c-mobile" data-rule="mobile" class="form-control" name="row[mobile]" type="text" >
    </div>
  </div>
  <div class="form-group">
    <label for="c-avatar" class="control-label col-xs-12 col-sm-2">{:__('Avatar')}:</label>
    <div class="col-xs-12 col-sm-8">
      <div class="input-group">
        <input id="c-avatar" data-rule="" class="form-control" size="50" name="row[avatar]" type="text" >
        <div class="input-group-addon no-border no-padding">
          <span><button type="button" id="faupload-avatar" class="btn btn-danger faupload" data-input-id="c-avatar" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-avatar"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
          <span><button type="button" id="fachoose-avatar" class="btn btn-primary fachoose" data-input-id="c-avatar" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
        </div>
        <span class="msg-box n-right" for="c-avatar"></span>
      </div>
      <ul class="row list-inline faupload-preview" id="p-avatar"></ul>
    </div>
  </div>

  <div class="form-group">
    <label for="c-remark" class="control-label col-xs-12 col-sm-2">{:__('Remark')}:</label>
    <div class="col-xs-12 col-sm-8">
      <textarea id="c-remark" data-rule="" class="form-control" name="row[remark]" rows="3" placeholder="请输入用户备注信息"></textarea>
    </div>
  </div>

  <div class="form-group">
    <label for="c-money" class="control-label col-xs-12 col-sm-2">{:__('Money')}:</label>
    <div class="col-xs-12 col-sm-4">
      <input id="c-money" data-rule="required" class="form-control" name="row[money]" type="number" >
    </div>
  </div>

  <div class="form-group">
    <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
    <div class="col-xs-12 col-sm-8">
      {:build_radios('row[status]', ['normal'=>__('Normal'), 'hidden'=>__('Hidden')])}
    </div>
  </div>
  <div class="form-group layer-footer">
    <label class="control-label col-xs-12 col-sm-2"></label>
    <div class="col-xs-12 col-sm-8">
      <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
      <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
    </div>
  </div>
</form>
