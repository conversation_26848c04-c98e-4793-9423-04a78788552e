<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>聊天管理</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <script src="__CDN__/assets/addons/lottery/js/vue.mini.js"></script>
    <link rel="stylesheet" href="__CDN__/assets/addons/lottery/css/element.css">
    <script src="__CDN__/assets/addons/lottery/js/element.js"></script>
</head>
<body>
<div id="chat-app" v-cloak>
    <el-container class="chat-container" :class="{ 'mobile': isMobile }">
        <!-- 左侧会话列表 -->
        <el-aside class="chat-sidebar" :class="{ 'mobile-hidden': isMobile && currentConversation }" :width="isMobile ? '100%' : '350px'">
            <!-- 头部搜索和新建 -->
            <div class="sidebar-header">
                <el-row :gutter="10">
                    <el-col :span="isMobile ? 20 : 18">
                        <el-input v-model="searchKeyword" placeholder="搜索会话..." prefix-icon="el-icon-search" :size="isMobile ? 'small' : 'medium'" @input="filterConversations"></el-input>
                    </el-col>
                    <el-col :span="isMobile ? 4 : 6">
                        <el-button type="primary" icon="el-icon-plus" @click="showNewChatDialog = true" :size="isMobile ? 'small' : 'medium'" circle></el-button>
                    </el-col>
                </el-row>
            </div>
            
            <!-- 会话列表 -->
            <div class="sidebar-content">
                <div v-if="conversations.length === 0" class="empty-state">
                    <i class="el-icon-chat-line-square"></i>
                    <span>暂无会话</span>
                </div>
                <div v-for="conversation in filteredConversations" :key="conversation.id" class="conversation-item" :class="{ active: currentConversation && currentConversation.id === conversation.id }" @click="selectConversation(conversation)">
                    <el-avatar :src="conversation.avatar || '/assets/img/avatar.png'" :size="isMobile ? 40 : 45"></el-avatar>
                    <div class="conversation-info">
                        <div class="conversation-name">{{ conversation.name || '未知用户' }}</div>
                        <div class="conversation-last-message">{{ conversation.last_message_content || '暂无消息' }}</div>
                    </div>
                    <div class="conversation-meta">
                        <div class="conversation-time">{{ formatTime(conversation.last_message_time) }}</div>
                        <el-badge v-if="conversation.unread_count > 0" :value="conversation.unread_count" :max="99" class="conversation-badge"></el-badge>
                    </div>
                </div>
            </div>
        </el-aside>

        <!-- 右侧聊天区域 -->
        <el-main class="chat-main" :class="{ 'mobile-show': isMobile && currentConversation }">
            <!-- 未选择会话时的欢迎页面 -->
            <div v-if="!currentConversation" class="chat-welcome">
                <div class="welcome-content">
                    <i class="el-icon-chat-dot-round"></i>
                    <h3>选择一个会话开始聊天</h3>
                    <p>
                        <span v-if="isSuperAdmin">作为超级管理员，您可以与所有用户和商户聊天</span>
                        <span v-else>您可以与店铺内的用户进行聊天</span>
                    </p>
                </div>
            </div>

            <!-- 聊天窗口 -->
            <div v-else class="chat-window">
                <!-- 聊天头部 -->
                <div class="chat-header">
                    <el-row type="flex" align="middle">
                        <el-col :span="isMobile ? 2 : 0">
                            <el-button v-if="isMobile" type="text" icon="el-icon-arrow-left" @click="backToConversationList" class="back-button"></el-button>
                        </el-col>
                        <el-col :span="isMobile ? 16 : 12">
                            <div class="header-user-info">
                                <el-avatar :src="currentConversation.avatar || '/assets/img/avatar.png'" :size="isMobile ? 35 : 40"></el-avatar>
                                <div class="user-info">
                                    <div class="user-name">{{ currentConversation.name || '未知用户' }}</div>
                                    <div class="user-status">{{ onlineStatus }}</div>
                                </div>
                            </div>
                        </el-col>
                        <el-col :span="isMobile ? 6 : 12" style="text-align: right;">
                            <el-button :size="isMobile ? 'mini' : 'small'" @click="clearHistory">
                                <i class="el-icon-delete"></i>
                                <span v-if="!isMobile"> 清空记录</span>
                            </el-button>
                        </el-col>
                    </el-row>
                </div>

                <!-- 消息区域 -->
                <div class="chat-messages" ref="messagesContainer">
                    <div v-if="messages.length === 0" class="empty-messages">
                        <span>暂无消息，开始聊天吧～</span>
                    </div>
                    <div v-for="message in messages" :key="message.id" class="message-item" :class="{ 'is-self': message.is_self }">
                        <div class="message-avatar">
                            <el-avatar :src="message.sender_info.avatar || '/assets/img/avatar.png'" :size="isMobile ? 30 : 35"></el-avatar>
                        </div>
                        <div class="message-content">
                            <div class="message-bubble">{{ message.content }}</div>
                            <div class="message-time">{{ formatTime(message.createtime) }}</div>
                        </div>
                    </div>
                    <div v-if="messagesLoading" class="loading-messages">
                        <i class="el-icon-loading"></i> 加载中...
                    </div>
                </div>

                <!-- 输入区域 -->
                <div class="chat-input">
                    <div class="input-toolbar" v-if="!isMobile">
                        <el-button size="mini" icon="el-icon-picture" title="图片"></el-button>
                        <el-button size="mini" icon="el-icon-paperclip" title="文件"></el-button>
                    </div>
                    <div class="input-container">
                        <el-row :gutter="10">
                            <el-col :span="isMobile ? 18 : 20">
                                <el-input v-model="messageInput" :type="isMobile ? 'text' : 'textarea'" :rows="isMobile ? 1 : 3" :placeholder="isMobile ? '输入消息...' : '输入消息，Ctrl+Enter 发送'" @keydown.ctrl.enter.native="sendMessage" @keydown.enter.native="isMobile && sendMessage" resize="none"></el-input>
                            </el-col>
                            <el-col :span="isMobile ? 6 : 4">
                                <el-button type="primary" @click="sendMessage" :loading="sendingMessage" :size="isMobile ? 'small' : 'medium'" :style="{ width: '100%', height: isMobile ? '32px' : '76px' }">发送</el-button>
                            </el-col>
                        </el-row>
                    </div>
                </div>
            </div>
        </el-main>
    </el-container>

    <!-- 新建聊天对话框 -->
    <el-dialog title="新建聊天" :visible.sync="showNewChatDialog" :width="isMobile ? '90%' : '600px'" :fullscreen="isMobile">
        <el-form>
            <el-form-item label="搜索用户">
                <el-input v-model="userSearchKeyword" placeholder="输入用户名或手机号搜索..." @input="searchUsers"></el-input>
            </el-form-item>
        </el-form>
        
        <div class="user-search-results">
            <div v-if="searchedUsers.length === 0 && userSearchKeyword" class="no-results">未找到用户</div>
            <div v-else-if="!userSearchKeyword" class="search-prompt">请输入关键词搜索用户</div>
            <div v-for="user in searchedUsers" :key="user.id" class="user-item" @click="createConversationWithUser(user)">
                <el-avatar :src="user.avatar || '/assets/img/avatar.png'" :size="40"></el-avatar>
                <div class="user-info">
                    <div class="user-name">{{ user.nickname || user.username }}</div>
                    <div class="user-mobile">{{ user.mobile || '' }}</div>
                </div>
            </div>
        </div>
    </el-dialog>
</div>

<!-- 引入聊天脚本 -->
<script>
new Vue({
    el: '#chat-app',
    data() {
        return {
            // 配置信息
            config: window.Config || {},
            
            // WebSocket连接
            ws: null,
            
            // 界面状态
            searchKeyword: '',
            showNewChatDialog: false,
            userSearchKeyword: '',
            messageInput: '',
            messagesLoading: false,
            sendingMessage: false,
            
            // 移动端检测
            isMobile: false,
            
            // 数据
            conversations: [],
            filteredConversations: [],
            currentConversation: null,
            messages: [],
            searchedUsers: [],
            
            // 用户信息
            isSuperAdmin: false,
            onlineStatus: '在线'
        }
    },
    
    mounted() {
        this.isSuperAdmin = this.config.is_super_admin;
        this.detectMobile();
        this.addMobileEventListeners();
        this.loadConversations();
        this.initWebSocket();
        
        // 监听窗口大小变化
        window.addEventListener('resize', this.handleResize);
    },
    
    beforeDestroy() {
        window.removeEventListener('resize', this.handleResize);
        if (this.ws) {
            this.ws.close();
        }
    },
    
    methods: {
        // 检测移动端
        detectMobile() {
            const userAgent = navigator.userAgent || navigator.vendor || window.opera;
            const screenWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
            const isMobileScreen = screenWidth <= 768;
            const isMobileAgent = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent.toLowerCase());
            this.isMobile = isMobileScreen || isMobileAgent;
        },
        
        // 处理窗口大小变化
        handleResize() {
            this.detectMobile();
        },
        
        // 添加移动端事件监听
        addMobileEventListeners() {
            if (!this.isMobile) return;
            
            // 阻止双击缩放
            document.addEventListener('touchstart', function(event) {
                if (event.touches.length > 1) {
                    event.preventDefault();
                }
            });
            
            let lastTouchEnd = 0;
            document.addEventListener('touchend', function(event) {
                const now = (new Date()).getTime();
                if (now - lastTouchEnd <= 300) {
                    event.preventDefault();
                }
                lastTouchEnd = now;
            }, false);
        },
        
        // 返回会话列表（移动端）
        backToConversationList() {
            this.currentConversation = null;
        },
        
        // 加载会话列表
        loadConversations() {
            this.ajaxRequest('/admin/lottery/chat/getConversations', 'GET', {}, (data) => {
                this.conversations = data || [];
                this.filteredConversations = [...this.conversations];
            }, (error) => {
                console.error('加载会话列表失败', error);
            });
        },
        
        // 过滤会话
        filterConversations() {
            if (!this.searchKeyword) {
                this.filteredConversations = [...this.conversations];
                return;
            }
            
            this.filteredConversations = this.conversations.filter(conv => {
                return (conv.name || '').toLowerCase().includes(this.searchKeyword.toLowerCase());
            });
        },
        
        // 选择会话
        selectConversation(conversation) {
            this.currentConversation = conversation;
            this.loadMessages(conversation.id);
        },
        
        // 加载消息列表
        loadMessages(conversationId) {
            this.messagesLoading = true;
            this.ajaxRequest('/admin/lottery/chat/getMessages', 'GET', {
                conversation_id: conversationId,
                page: 1,
                limit: 50
            }, (data) => {
                this.messages = data || [];
                this.$nextTick(() => {
                    this.scrollToBottom();
                });
                this.messagesLoading = false;
            }, (error) => {
                console.error('加载消息失败', error);
                this.messagesLoading = false;
            });
        },
        
        // 发送消息
        sendMessage() {
            if (!this.messageInput.trim()) {
                this.$message.warning('请输入消息内容');
                return;
            }
            
            if (!this.currentConversation) {
                this.$message.warning('请先选择会话');
                return;
            }
            
            this.sendingMessage = true;
            this.ajaxRequest('/admin/lottery/chat/sendMessage', 'POST', {
                conversation_id: this.currentConversation.id,
                content: this.messageInput.trim(),
                type: 1
            }, (data) => {
                this.messageInput = '';
                this.addMessageToList(data);
                this.$nextTick(() => {
                    this.scrollToBottom();
                });
                this.sendingMessage = false;
            }, (error) => {
                console.error('发送消息失败', error);
                this.sendingMessage = false;
            });
        },
        
        // 添加消息到列表
        addMessageToList(message) {
            if (message && this.currentConversation && message.conversation_id == this.currentConversation.id) {
                this.messages.push(message);
            }
        },
        
        // 搜索用户
        searchUsers() {
            if (!this.userSearchKeyword.trim()) {
                this.searchedUsers = [];
                return;
            }
            
            this.ajaxRequest('/admin/lottery/chat/getUsers', 'GET', {
                keyword: this.userSearchKeyword.trim(),
                page: 1,
                limit: 20
            }, (data) => {
                this.searchedUsers = data.list || [];
            }, (error) => {
                console.error('搜索用户失败', error);
                this.searchedUsers = [];
            });
        },
        
        // 创建与用户的会话
        createConversationWithUser(user) {
            this.ajaxRequest('/admin/lottery/chat/createConversation', 'POST', {
                user_id: user.id
            }, (data) => {
                this.showNewChatDialog = false;
                this.userSearchKeyword = '';
                this.searchedUsers = [];
                this.loadConversations();
                
                // 选择新创建的会话
                setTimeout(() => {
                    this.selectConversation(data);
                }, 500);
            }, (error) => {
                console.error('创建会话失败', error);
            });
        },
        
        // 清空聊天记录
        clearHistory() {
            this.$confirm('确定要清空聊天记录吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                // TODO: 实现清空聊天记录接口
                this.$message.success('清空成功');
            });
        },
        
        // 初始化WebSocket
        initWebSocket() {
            if (!this.config.ws) {
                console.log('WebSocket URL未配置');
                return;
            }
            
            try {
                const wsUrl = this.config.ws + '?admin_token=' + this.getAdminToken();
                this.ws = new WebSocket(wsUrl);
                
                this.ws.onopen = () => {
                    console.log('WebSocket连接已建立');
                };
                
                this.ws.onmessage = (event) => {
                    const data = JSON.parse(event.data);
                    this.handleWebSocketMessage(data);
                };
                
                this.ws.onclose = () => {
                    console.log('WebSocket连接已关闭');
                    // 重连逻辑
                    setTimeout(() => {
                        this.initWebSocket();
                    }, 5000);
                };
                
                this.ws.onerror = (error) => {
                    console.error('WebSocket错误:', error);
                };
            } catch (e) {
                console.error('WebSocket初始化失败:', e);
            }
        },
        
        // 获取管理员Token
        getAdminToken() {
            return 'admin_' + this.config.admin_id + '_' + Date.now();
        },
        
        // 处理WebSocket消息
        handleWebSocketMessage(data) {
            switch (data.type) {
                case 'new_message':
                    this.addMessageToList(data.message);
                    this.$nextTick(() => {
                        this.scrollToBottom();
                    });
                    break;
                case 'new_conversation':
                    this.loadConversations();
                    break;
                case 'ping':
                    // 响应心跳包
                    this.ws.send(JSON.stringify({type: 'pong'}));
                    break;
            }
        },
        
        // 滚动到底部
        scrollToBottom() {
            if (this.$refs.messagesContainer) {
                this.$refs.messagesContainer.scrollTop = this.$refs.messagesContainer.scrollHeight;
            }
        },
        
        // 格式化时间
        formatTime(timestamp) {
            if (!timestamp) return '';
            
            const date = new Date(timestamp * 1000);
            const now = new Date();
            
            if (date.toDateString() === now.toDateString()) {
                // 今天，显示时间
                return date.toTimeString().substring(0, 5);
            } else if (date.getFullYear() === now.getFullYear()) {
                // 今年，显示月-日
                return (date.getMonth() + 1) + '-' + date.getDate();
            } else {
                // 其他年份，显示年-月-日
                return date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate();
            }
        },
        
        // Ajax请求封装
        ajaxRequest(url, method, data, successCallback, errorCallback) {
            const xhr = new XMLHttpRequest();
            xhr.open(method, url, true);
            xhr.setRequestHeader('Content-Type', 'application/json');
            xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            if (response.code === 1) {
                                successCallback(response.data);
                            } else {
                                console.error('请求失败:', response.msg);
                                if (errorCallback) errorCallback(response.msg);
                            }
                        } catch (e) {
                            console.error('解析响应失败:', e);
                            if (errorCallback) errorCallback('解析响应失败');
                        }
                    } else {
                        console.error('请求失败:', xhr.status);
                        if (errorCallback) errorCallback('网络请求失败');
                    }
                }
            };
            
            if (method === 'GET') {
                const params = new URLSearchParams(data).toString();
                xhr.send(params ? `?${params}` : null);
            } else {
                xhr.send(JSON.stringify(data));
            }
        }
    }
});
</script>

<style>
[v-cloak] { display: none; }

/* Base container styles */
.chat-container { height: calc(100vh - 20px); border: 1px solid #eee; }
.chat-container.mobile { height: 100vh; border: none; }

/* Sidebar styles */
.chat-sidebar { background-color: #f5f5f5; border-right: 1px solid #e6e6e6; position: relative; }
.sidebar-header { padding: 15px; background: #fff; border-bottom: 1px solid #e6e6e6; }
.sidebar-content { height: calc(100vh - 100px); overflow-y: auto; }

/* Mobile responsive sidebar */
@media (max-width: 768px) {
    .chat-sidebar { position: fixed; top: 0; left: 0; height: 100vh; z-index: 1000; transition: transform 0.3s ease; }
    .chat-sidebar.mobile-hidden { transform: translateX(-100%); }
    .sidebar-content { height: calc(100vh - 60px); }
    .sidebar-header { padding: 10px; }
}

/* Empty state */
.empty-state { text-align: center; padding: 40px; color: #999; }
.empty-state i { font-size: 48px; display: block; margin-bottom: 10px; }

/* Conversation item */
.conversation-item { display: flex; align-items: center; padding: 12px 15px; cursor: pointer; border-bottom: 1px solid #f0f0f0; transition: background-color 0.2s; min-height: 70px; }
.conversation-item:hover { background-color: #f8f9fa; }
.conversation-item.active { background-color: #409eff; color: white; }
.conversation-info { flex: 1; margin-left: 12px; min-width: 0; }
.conversation-name { font-weight: bold; margin-bottom: 4px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; font-size: 14px; }
.conversation-last-message { font-size: 12px; color: #999; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; }
.conversation-item.active .conversation-last-message { color: rgba(255, 255, 255, 0.8); }
.conversation-meta { display: flex; flex-direction: column; align-items: flex-end; }
.conversation-time { font-size: 11px; color: #999; margin-bottom: 4px; }
.conversation-item.active .conversation-time { color: rgba(255, 255, 255, 0.8); }

/* Main chat area */
.chat-main { padding: 0; position: relative; }
@media (max-width: 768px) {
    .chat-main { position: fixed; top: 0; left: 0; width: 100vw; height: 100vh; background: #fff; z-index: 999; transform: translateX(100%); transition: transform 0.3s ease; }
    .chat-main.mobile-show { transform: translateX(0); }
}

/* Welcome screen */
.chat-welcome { height: 100%; display: flex; align-items: center; justify-content: center; background-color: #fafafa; }
.welcome-content { text-align: center; }
.welcome-content i { font-size: 120px; color: #ddd; margin-bottom: 20px; }
.welcome-content h3 { color: #999; margin-bottom: 10px; }
.welcome-content p { color: #ccc; }

/* Chat window */
.chat-window { height: 100%; display: flex; flex-direction: column; }

/* Chat header */
.chat-header { padding: 15px 20px; border-bottom: 1px solid #e6e6e6; background-color: #fff; min-height: 70px; }
.header-user-info { display: flex; align-items: center; }
.user-info { margin-left: 12px; }
.user-name { font-weight: bold; font-size: 14px; margin-bottom: 2px; }
.user-status { font-size: 12px; color: #999; }

/* Messages area */
.chat-messages { flex: 1; padding: 15px 20px; overflow-y: auto; background-color: #f8f9fa; }
.empty-messages { text-align: center; padding: 40px; color: #999; }
.loading-messages { text-align: center; padding: 20px; }
.message-item { display: flex; margin-bottom: 15px; align-items: flex-start; }
.message-item.is-self { flex-direction: row-reverse; }
.message-avatar { margin: 0 10px; }
.message-content { max-width: 60%; }
.message-bubble { padding: 10px 15px; border-radius: 18px; background-color: #fff; box-shadow: 0 1px 2px rgba(0,0,0,0.1); word-wrap: break-word; line-height: 1.4; }
.message-item.is-self .message-bubble { background-color: #409eff; color: white; }
.message-time { font-size: 11px; color: #999; margin-top: 5px; text-align: center; }

/* Input area */
.chat-input { border-top: 1px solid #e6e6e6; background-color: #fff; }
.input-toolbar { padding: 8px 15px; border-bottom: 1px solid #f0f0f0; }
.input-container { padding: 10px 15px; }

/* Dialog styles */
.user-search-results { max-height: 300px; overflow-y: auto; margin-top: 15px; }
.no-results, .search-prompt { text-align: center; padding: 20px; color: #999; }
.user-item { display: flex; align-items: center; padding: 10px; cursor: pointer; border-radius: 4px; margin-bottom: 8px; transition: background-color 0.2s; }
.user-item:hover { background-color: #f8f9fa; }
.user-info { margin-left: 12px; }
.user-name { font-weight: bold; margin-bottom: 2px; }
.user-mobile { font-size: 12px; color: #999; }

/* Mobile optimizations */
@media (max-width: 768px) {
    .conversation-item { padding: 10px; min-height: 60px; }
    .conversation-name { font-size: 13px; }
    .conversation-last-message { font-size: 11px; }
    .chat-header { padding: 10px 15px; min-height: 50px; }
    .user-name { font-size: 13px; }
    .user-status { font-size: 11px; }
    .chat-messages { padding: 10px 15px; }
    .message-content { max-width: 70%; }
    .message-bubble { padding: 8px 12px; font-size: 14px; }
    .input-container { padding: 8px 10px; }
}
</style>
</body>
</html>
