<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>彩票统计</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <script src="__CDN__/assets/addons/lottery/js/vue.mini.js"></script>
    <link rel="stylesheet" href="__CDN__/assets/addons/lottery/css/element.css">
    <script src="__CDN__/assets/addons/lottery/js/element.js"></script>
</head>
<body>
<div id="statistics-app" v-cloak>
    <el-card class="box-card">
        <div slot="header" class="clearfix">
            <span>彩票数据统计</span>
        </div>
        
        <!-- 筛选条件 -->
        <el-row :gutter="20" style="margin-bottom: 20px;">
            <el-col :span="8">
                <el-select 
                    v-model="filters.store_id" 
                    placeholder="选择店铺" 
                    clearable 
                    @change="loadData"
                    :disabled="adminStoreId > 0">
                    <el-option 
                        v-if="adminStoreId == 0"
                        label="全部店铺" 
                        value="">
                    </el-option>
                    <el-option 
                        v-for="store in stores" 
                        :key="store.id" 
                        :label="store.name" 
                        :value="store.id">
                    </el-option>
                </el-select>
            </el-col>
            <el-col :span="12">
                <el-date-picker
                    v-model="filters.date_range"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
                    @change="loadData">
                </el-date-picker>
            </el-col>
            <el-col :span="4">
                <el-button type="primary" @click="loadData" :loading="loading">
                    <i class="el-icon-search"></i> 查询
                </el-button>
            </el-col>
        </el-row>

        <!-- 概览统计卡片 -->
        <el-row :gutter="20" style="margin-bottom: 20px;">
            <el-col :span="6">
                <el-card class="overview-card">
                    <div class="overview-item">
                        <div class="overview-number">{{ overview.total_orders || 0 }}</div>
                        <div class="overview-label">总订单数</div>
                    </div>
                </el-card>
            </el-col>
            <el-col :span="6">
                <el-card class="overview-card">
                    <div class="overview-item">
                        <div class="overview-number">￥{{ formatMoney(overview.total_bet_amount || 0) }}</div>
                        <div class="overview-label">总投注金额</div>
                    </div>
                </el-card>
            </el-col>
            <el-col :span="6">
                <el-card class="overview-card">
                    <div class="overview-item">
                        <div class="overview-number">￥{{ formatMoney(overview.total_prize_amount || 0) }}</div>
                        <div class="overview-label">总奖金金额</div>
                    </div>
                </el-card>
            </el-col>
            <el-col :span="6">
                <el-card class="overview-card">
                    <div class="overview-item">
                        <div class="overview-number" :class="profitClass">￥{{ formatMoney(overview.profit_loss || 0) }}</div>
                        <div class="overview-label">盈亏金额</div>
                    </div>
                </el-card>
            </el-col>
        </el-row>

        <el-row :gutter="20" style="margin-bottom: 20px;">
            <el-col :span="6">
                <el-card class="overview-card">
                    <div class="overview-item">
                        <div class="overview-number">{{ memberFundOverview.total_members || 0 }}</div>
                        <div class="overview-label">总用户数</div>
                    </div>
                </el-card>
            </el-col>
            <el-col :span="6">
                <el-card class="overview-card">
                    <div class="overview-item">
                        <div class="overview-number">{{ overview.win_rate || 0 }}%</div>
                        <div class="overview-label">中奖率</div>
                    </div>
                </el-card>
            </el-col>
            <el-col :span="6">
                <el-card class="overview-card">
                    <div class="overview-item">
                        <div class="overview-number">￥{{ formatMoney(overview.avg_bet_amount || 0) }}</div>
                        <div class="overview-label">平均投注金额</div>
                    </div>
                </el-card>
            </el-col>
            <el-col :span="6">
                <el-card class="overview-card">
                    <div class="overview-item">
                        <div class="overview-number">{{ overview.winning_orders || 0 }}</div>
                        <div class="overview-label">中奖订单数</div>
                    </div>
                </el-card>
            </el-col>
        </el-row>

        <!-- 详细统计表格 -->
        <el-tabs v-model="activeTab" @tab-click="handleTabClick">
            <!-- 会员资金统计 -->
            <el-tab-pane label="会员资金统计" name="member_fund">
                <!-- 会员资金概览卡片 -->
                <el-row :gutter="20" style="margin-bottom: 20px;">
                    <el-col :span="6">
                        <el-card class="overview-card">
                            <div class="overview-item">
                                <div class="overview-number">{{ memberFundOverview.total_members || 0 }}</div>
                                <div class="overview-label">总会员数</div>
                            </div>
                        </el-card>
                    </el-col>
                    <el-col :span="6">
                        <el-card class="overview-card">
                            <div class="overview-item">
                                <div class="overview-number">￥{{ formatMoney(memberFundOverview.total_recharge || 0) }}</div>
                                <div class="overview-label">总充值额</div>
                            </div>
                        </el-card>
                    </el-col>
                    <el-col :span="6">
                        <el-card class="overview-card">
                            <div class="overview-item">
                                <div class="overview-number">￥{{ formatMoney(memberFundOverview.total_withdrawal || 0) }}</div>
                                <div class="overview-label">总提现额</div>
                            </div>
                        </el-card>
                    </el-col>
                    <el-col :span="6">
                        <el-card class="overview-card">
                            <div class="overview-item">
                                <div class="overview-number">￥{{ formatMoney(memberFundOverview.total_prize || 0) }}</div>
                                <div class="overview-label">总中奖额</div>
                            </div>
                        </el-card>
                    </el-col>
                </el-row>

                <!-- 搜索栏 -->
                <el-row :gutter="20" style="margin-bottom: 20px;">
                    <el-col :span="8">
                        <el-input
                            v-model="memberFundSearch"
                            placeholder="搜索用户名或手机号"
                            @keyup.enter.native="loadMemberFundData"
                            clearable>
                            <el-button slot="append" icon="el-icon-search" @click="loadMemberFundData"></el-button>
                        </el-input>
                    </el-col>
                </el-row>

                <!-- 会员资金统计表格 -->
                <el-table
                    :data="memberFundList"
                    stripe
                    v-loading="memberFundLoading"
                    @sort-change="handleMemberFundSort">
                    <el-table-column prop="user_id" label="会员ID" min-width="80" sortable="custom"></el-table-column>
                    <el-table-column prop="username" label="用户名" min-width="100" sortable="custom"></el-table-column>
                    <el-table-column prop="mobile" label="手机号" min-width="120" sortable="custom"></el-table-column>
                    <el-table-column label="充值总额" min-width="110" sortable="custom" prop="total_recharge">
                        <template slot-scope="scope">
                            <span style="color: #67C23A;">￥{{ formatMoney(scope.row.total_recharge) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="提现总额" min-width="110" sortable="custom" prop="total_withdrawal">
                        <template slot-scope="scope">
                            <span style="color: #F56C6C;">￥{{ formatMoney(scope.row.total_withdrawal) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="中奖总额" min-width="110" sortable="custom" prop="total_prize">
                        <template slot-scope="scope">
                            <span style="color: #E6A23C;">￥{{ formatMoney(scope.row.total_prize) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="当前余额" min-width="110" sortable="custom" prop="current_balance">
                        <template slot-scope="scope">
                            <span style="color: #409EFF;">￥{{ formatMoney(scope.row.current_balance) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="注册时间" min-width="140" sortable="custom" prop="register_time">
                        <template slot-scope="scope">
                            {{ formatDateTime(scope.row.register_time) }}
                        </template>
                    </el-table-column>
                    <el-table-column label="备注" min-width="120" show-overflow-tooltip>
                        <template slot-scope="scope">
                            <span v-if="scope.row.remark" style="color: #606266;">{{ scope.row.remark }}</span>
                            <span v-else style="color: #C0C4CC;">暂无备注</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" min-width="260" fixed="right">
                        <template slot-scope="scope">
                            <el-button-group>
                                <el-button
                                    type="primary"
                                    size="mini"
                                    @click="viewUserRecharge(scope.row.user_id)"
                                    title="查看充值记录">
                                    充值记录
                                </el-button>
                                <el-button
                                    type="warning"
                                    size="mini"
                                    @click="viewUserWithdrawal(scope.row.user_id)"
                                    title="查看提现记录">
                                    提现记录
                                </el-button>
                                <el-button
                                    type="info"
                                    size="mini"
                                    @click="viewUserOrders(scope.row.user_id)"
                                    title="查看下注记录">
                                    下注记录
                                </el-button>
                            </el-button-group>
                        </template>
                    </el-table-column>
                </el-table>

                <!-- 分页 -->
                <el-pagination
                    @size-change="handleMemberFundSizeChange"
                    @current-change="handleMemberFundCurrentChange"
                    :current-page="memberFundPagination.page"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="memberFundPagination.limit"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="memberFundPagination.total"
                    style="margin-top: 20px; text-align: left;">
                </el-pagination>
            </el-tab-pane>

            <!-- 店铺统计 - 只有超级管理员(store_id=0)才显示 -->
            <el-tab-pane v-if="adminStoreId == 0" label="店铺统计" name="store">
                <el-table :data="storeStats" stripe>
                    <el-table-column prop="store_name" label="店铺名称" width="200"></el-table-column>
                    <el-table-column prop="order_count" label="订单数量" width="120"></el-table-column>
                    <el-table-column label="投注金额" width="150">
                        <template slot-scope="scope">
                            ￥{{ formatMoney(scope.row.total_amount) }}
                        </template>
                    </el-table-column>
                    <el-table-column label="奖金金额" width="150">
                        <template slot-scope="scope">
                            ￥{{ formatMoney(scope.row.total_prize) }}
                        </template>
                    </el-table-column>
                    <el-table-column label="盈亏金额" width="150">
                        <template slot-scope="scope">
                            <span :class="scope.row.profit >= 0 ? 'profit-positive' : 'profit-negative'">
                                ￥{{ formatMoney(scope.row.profit) }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="user_count" label="用户数量" width="120"></el-table-column>
                </el-table>
            </el-tab-pane>

            <!-- 彩种统计 -->
            <el-tab-pane label="彩种统计" name="lottery">
                <el-table :data="lotteryTypeStats" stripe>
                    <el-table-column label="彩种类型" width="120">
                        <template slot-scope="scope">
                            {{ getLotteryTypeName(scope.row.lottery_type) }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="order_count" label="订单数量" width="120"></el-table-column>
                    <el-table-column label="投注金额" width="150">
                        <template slot-scope="scope">
                            ￥{{ formatMoney(scope.row.total_amount) }}
                        </template>
                    </el-table-column>
                    <el-table-column label="奖金金额" width="150">
                        <template slot-scope="scope">
                            ￥{{ formatMoney(scope.row.total_prize) }}
                        </template>
                    </el-table-column>
                    <el-table-column label="平均投注" width="150">
                        <template slot-scope="scope">
                            ￥{{ formatMoney(scope.row.avg_amount) }}
                        </template>
                    </el-table-column>
                </el-table>
            </el-tab-pane>

            <!-- 财务统计 -->
            <el-tab-pane label="财务统计" name="finance">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-card>
                            <div slot="header">充值统计</div>
                            <div class="finance-item">
                                <span>充值笔数：</span>
                                <span>{{ financeStats.recharge?.recharge_count || 0 }} 笔</span>
                            </div>
                            <div class="finance-item">
                                <span>充值总额：</span>
                                <span>￥{{ formatMoney(financeStats.recharge?.total_recharge || 0) }}</span>
                            </div>
                            <div class="finance-item">
                                <span>充值手续费：</span>
                                <span>￥{{ formatMoney(financeStats.recharge?.total_recharge_fee || 0) }}</span>
                            </div>
                        </el-card>
                    </el-col>
                    <el-col :span="12">
                        <el-card>
                            <div slot="header">提现统计</div>
                            <div class="finance-item">
                                <span>提现笔数：</span>
                                <span>{{ financeStats.withdrawal?.withdrawal_count || 0 }} 笔</span>
                            </div>
                            <div class="finance-item">
                                <span>提现总额：</span>
                                <span>￥{{ formatMoney(financeStats.withdrawal?.total_withdrawal || 0) }}</span>
                            </div>
                            <div class="finance-item">
                                <span>提现手续费：</span>
                                <span>￥{{ formatMoney(financeStats.withdrawal?.total_withdrawal_fee || 0) }}</span>
                            </div>
                        </el-card>
                    </el-col>
                </el-row>
            </el-tab-pane>

            <!-- 用户统计 -->
            <el-tab-pane label="用户统计" name="user">
                <el-row :gutter="20">
                    <el-col :span="8">
                        <el-card>
                            <div class="finance-item">
                                <span>总用户数：</span>
                                <span>{{ userStats.total_users || 0 }}</span>
                            </div>
                            <div class="finance-item">
                                <span>今日新增：</span>
                                <span>{{ userStats.new_users_today || 0 }}</span>
                            </div>
                            <div class="finance-item">
                                <span>今日活跃：</span>
                                <span>{{ userStats.active_users_today || 0 }}</span>
                            </div>
                        </el-card>
                    </el-col>
                    <el-col :span="8">
                        <el-card>
                            <div class="finance-item">
                                <span>用户总余额：</span>
                                <span>￥{{ formatMoney(userStats.total_balance || 0) }}</span>
                            </div>
                            <div class="finance-item">
                                <span>平均余额：</span>
                                <span>￥{{ formatMoney(userStats.avg_balance || 0) }}</span>
                            </div>
                        </el-card>
                    </el-col>
                </el-row>
            </el-tab-pane>
        </el-tabs>
    </el-card>
</div>



<style>
[v-cloak] {
    display: none !important;
}

.overview-card {
    text-align: center;
    margin-bottom: 10px;
}

.overview-item {
    padding: 20px 0;
}

.overview-number {
    font-size: 28px;
    font-weight: bold;
    color: #409EFF;
    margin-bottom: 5px;
}

.overview-label {
    font-size: 14px;
    color: #999;
}

.profit-positive {
    color: #67C23A;
    font-weight: bold;
}

.profit-negative {
    color: #F56C6C;
    font-weight: bold;
}

.finance-item {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.finance-item:last-child {
    border-bottom: none;
}

.box-card {
    margin: 20px;
}

.el-table {
    margin-top: 20px;
}
</style>
</body>
</html>