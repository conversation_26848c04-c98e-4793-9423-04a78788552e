<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('League')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-league" class="form-control" name="row[league]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Match_date')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-match_date" class="form-control" name="row[match_date]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Match_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-match_time" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[match_time]" type="text" value="{:date('Y-m-d H:i:s')}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Match_num')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-match_num" class="form-control" name="row[match_num]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Weekday')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-weekday" class="form-control" name="row[weekday]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-status" data-rule="required" class="form-control" name="row[status]" type="number" value="1">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Draw_status')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-draw_status" data-rule="required" class="form-control" name="row[draw_status]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Team1_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-team1_id" data-rule="required" data-source="team1/index" class="form-control selectpage" name="row[team1_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Team1_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-team1_name" class="form-control" name="row[team1_name]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Team1_logo')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-team1_logo" class="form-control" name="row[team1_logo]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Team2_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-team2_id" data-rule="required" data-source="team2/index" class="form-control selectpage" name="row[team2_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Team2_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-team2_name" class="form-control" name="row[team2_name]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Team2_logo')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-team2_logo" class="form-control" name="row[team2_logo]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Odds_data')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-odds_data" class="form-control " rows="5" name="row[odds_data]" cols="50"></textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Pool_data')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-pool_data" class="form-control " rows="5" name="row[pool_data]" cols="50"></textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Result_data')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-result_data" class="form-control " rows="5" name="row[result_data]" cols="50"></textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Sort')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-sort" data-rule="required" class="form-control" name="row[sort]" type="number" value="0">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
