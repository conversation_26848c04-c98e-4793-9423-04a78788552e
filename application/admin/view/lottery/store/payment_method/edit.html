<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Store_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-store_id" data-rule="required" min="0" data-source="store/index" class="form-control selectpage" name="row[store_id]" type="text" value="{$row.store_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Method_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-method_name" data-rule="required" class="form-control" name="row[method_name]" type="text" value="{$row.method_name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Method_type')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-method_type" data-rule="required" class="form-control selectpicker" name="row[method_type]">
                {foreach name="methodTypeList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.method_type"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Account_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-account_name" data-rule="required" class="form-control" name="row[account_name]" type="text" value="{$row.account_name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Account_number')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-account_number" data-rule="required" class="form-control" name="row[account_number]" type="text" value="{$row.account_number|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Bank_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-bank_name" class="form-control" name="row[bank_name]" type="text" value="{$row.bank_name|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Bank_branch')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-bank_branch" class="form-control" name="row[bank_branch]" type="text" value="{$row.bank_branch|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Qr_code_url')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-qr_code_url" class="form-control" name="row[qr_code_url]" type="text" value="{$row.qr_code_url|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-status" data-rule="required" class="form-control" name="row[status]" type="number" value="{$row.status|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Sort_order')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-sort_order" data-rule="required" class="form-control" name="row[sort_order]" type="number" value="{$row.sort_order|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Remark')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-remark" class="form-control" name="row[remark]" type="text" value="{$row.remark|htmlentities}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
