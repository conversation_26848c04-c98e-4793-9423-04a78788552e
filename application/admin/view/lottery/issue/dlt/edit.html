<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Sale_start_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-sale_start_time" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[sale_start_time]" type="text" value="{:$row.sale_start_time?datetime($row.sale_start_time):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Sale_end_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-sale_end_time" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[sale_end_time]" type="text" value="{:$row.sale_end_time?datetime($row.sale_end_time):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Draw_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-draw_time" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[draw_time]" type="text" value="{:$row.draw_time?datetime($row.draw_time):''}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Numbers')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-numbers" class="form-control" name="row[numbers]" type="text" value="{$row.numbers|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-status" data-rule="required" class="form-control" name="row[status]" type="number" value="{$row.status|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Bonus_data')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-bonus_data" class="form-control " rows="5" name="row[bonus_data]" cols="50">{$row.bonus_data|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Draw_status')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-draw_status" class="form-control" name="row[draw_status]" type="number" value="{$row.draw_status|htmlentities}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
