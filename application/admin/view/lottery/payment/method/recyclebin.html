<div class="panel panel-default panel-intro">
    {:build_heading()}

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        {:build_toolbar('refresh')}
                        <a class="btn btn-info btn-multi btn-disabled disabled {:$auth->check('lottery/payment/method/restore')?'':'hide'}" href="javascript:;" data-url="lottery/payment/method/restore" data-action="restore"><i class="fa fa-rotate-left"></i> {:__('Restore')}</a>
                        <a class="btn btn-danger btn-multi btn-disabled disabled {:$auth->check('lottery/payment/method/destroy')?'':'hide'}" href="javascript:;" data-url="lottery/payment/method/destroy" data-action="destroy"><i class="fa fa-times"></i> {:__('Destroy')}</a>
                        <a class="btn btn-success btn-restoreall {:$auth->check('lottery/payment/method/restore')?'':'hide'}" href="javascript:;" data-url="lottery/payment/method/restore" title="{:__('Restore all')}"><i class="fa fa-rotate-left"></i> {:__('Restore all')}</a>
                        <a class="btn btn-danger btn-destroyall {:$auth->check('lottery/payment/method/destroy')?'':'hide'}" href="javascript:;" data-url="lottery/payment/method/destroy" title="{:__('Destroy all')}"><i class="fa fa-times"></i> {:__('Destroy all')}</a>
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover"
                           data-operate-restore="{:$auth->check('lottery/payment/method/restore')}"
                           data-operate-destroy="{:$auth->check('lottery/payment/method/destroy')}"
                           width="100%">
                    </table>
                </div>
            </div>

        </div>
    </div>
</div>
