<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="">
    <style>
        [v-cloak] {
            display: none !important;
        }

        .order-detail-container {
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .section-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #303133;
        }

        .amount-highlight {
            color: #E6A23C;
            font-weight: bold;
        }

        .status-success {
            color: #67C23A;
        }

        .status-warning {
            color: #E6A23C;
        }

        .status-danger {
            color: #F56C6C;
        }

        .status-info {
            color: #409EFF;
        }

        .ticket-image {
            max-width: 200px;
            max-height: 150px;
            margin: 5px;
            border: 1px solid #DCDFE6;
            border-radius: 4px;
        }

        .el-descriptions-item__label {
            font-weight: bold;
        }

        .upload-section {
            margin-bottom: 20px;
        }

        .upload-tip {
            color: #909399;
            font-size: 12px;
            margin-top: 5px;
        }

        .ticket-actions {
            margin-top: 20px;
            text-align: center;
        }
        .selected-images {
            margin: 15px 0;
        }
        .image-preview-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        .image-preview-item {
            position: relative;
            width: 120px;
            height: 120px;
        }
        .preview-image {
            width: 100%;
            height: 100%;
            border-radius: 6px;
        }
        .image-actions {
            position: absolute;
            top: 5px;
            right: 5px;
        }
        .image-slot {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
            background: #f5f7fa;
            color: #909399;
            font-size: 30px;
        }
        /* 足球投注详情样式 */
        .bet-details {
            margin-top: 20px;
        }
        .bet-details-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .match-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .match-item {
            width: 100%;
        }
        .match-card {
            border-radius: 8px;
        }
        .match-header {
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #EBEEF5;
        }
        .match-title {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .match-num {
            background: #409EFF;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            min-width: 50px;
            text-align: center;
        }
        .team-name {
            font-weight: bold;
            color: #303133;
            font-size: 14px;
        }
        .vs {
            color: #909399;
            font-size: 12px;
            margin: 0 5px;
        }
        .match-day {
            color: #909399;
            font-size: 12px;
            background: #f5f7fa;
            padding: 2px 6px;
            border-radius: 3px;
            margin-left: auto;
        }
        .bet-options {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        .bet-option {
            flex: 0 0 auto;
        }
        .option-tag {
            font-size: 13px;
            padding: 6px 12px;
        }
        /* 修改赔率对话框样式 */
        .modify-odds-content {
            max-height: 500px;
            overflow-y: auto;
        }
        .modify-match-item {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #EBEEF5;
            border-radius: 6px;
        }
        .modify-match-header {
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #EBEEF5;
        }
        .modify-match-header h5 {
            margin: 0;
            color: #303133;
            font-size: 14px;
        }
        .modify-options {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        .modify-option-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 8px 0;
        }
        .option-info {
            flex: 0 0 auto;
        }
        .option-odds {
            flex: 0 0 auto;
        }

        /* 大乐透相关样式 */
        .dlt-bet-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .dlt-bet-card {
            border: 1px solid #EBEEF5;
        }
        .dlt-bet-header {
            margin-bottom: 15px;
        }
        .dlt-bet-title {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .bet-index {
            font-weight: bold;
            color: #303133;
        }
        .bet-multiple {
            color: #E6A23C;
            font-weight: bold;
        }
        .dlt-numbers {
            margin-top: 10px;
        }
        .number-section {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        .section-label {
            font-weight: bold;
            color: #606266;
            margin-right: 10px;
            min-width: 50px;
        }
        .number-balls {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        .number-ball {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            font-weight: bold;
            font-size: 14px;
            color: white;
        }
        .front-ball {
            background-color: #E6A23C;
        }
        .back-ball {
            background-color: #409EFF;
        }

        /* 七星彩相关样式 */
        .qxc-bet-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .qxc-bet-card {
            border: 1px solid #EBEEF5;
        }
        .qxc-bet-header {
            margin-bottom: 15px;
        }
        .qxc-bet-title {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .qxc-numbers {
            margin-top: 10px;
        }
        .qxc-number-section {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        .qxc-number-balls {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        .qxc-number-ball {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 36px;
            height: 36px;
            border-radius: 8px;
            font-weight: bold;
            font-size: 16px;
            color: white;
            background-color: #67C23A;
            border: 2px solid #5daf34;
        }
        /* 七星彩每个位置使用不同的颜色 */
        .qxc-number-ball.position-1 { background-color: #F56C6C; border-color: #f04747; }
        .qxc-number-ball.position-2 { background-color: #E6A23C; border-color: #d48806; }
        .qxc-number-ball.position-3 { background-color: #67C23A; border-color: #5daf34; }
        .qxc-number-ball.position-4 { background-color: #409EFF; border-color: #3788d8; }
        .qxc-number-ball.position-5 { background-color: #909399; border-color: #82848a; }
        .qxc-number-ball.position-6 { background-color: #722ED1; border-color: #642ab5; }
        .qxc-number-ball.position-7 { background-color: #EB2F96; border-color: #d91a72; }

        /* 排列三相关样式 */
        .pls-bet-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .pls-bet-card {
            border: 1px solid #EBEEF5;
        }
        .pls-bet-header {
            margin-bottom: 15px;
        }
        .pls-bet-title {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .pls-numbers {
            margin-top: 10px;
        }
        .pls-number-section {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        .pls-number-balls, .plw-number-balls {
            display: flex;
            gap: 6px;
            flex-wrap: wrap;
        }
        .pls-number-ball {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            font-weight: bold;
            font-size: 18px;
            color: white;
            background-color: #67C23A;
            border: 3px solid #5daf34;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        /* 排列三每个位置使用不同的颜色，更加紧凑 */
        .pls-number-ball.position-1 {
            background: linear-gradient(135deg, #FF6B6B, #FF5252);
            border-color: #f04747;
        }
        .pls-number-ball.position-2 {
            background: linear-gradient(135deg, #4ECDC4, #26A69A);
            border-color: #26A69A;
        }
        .pls-number-ball.position-3 {
            background: linear-gradient(135deg, #45B7D1, #2196F3);
            border-color: #2196F3;
        }

        /* 排列三位置显示样式 */
        .pls-position-numbers {
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
        }
        .pls-position-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .position-label {
            font-weight: bold;
            color: #606266;
            font-size: 14px;
            min-width: 40px;
        }

        /* 排列5相关样式 */
        .plw-bet-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .plw-bet-card {
            border: 1px solid #EBEEF5;
        }
        .plw-bet-header {
            margin-bottom: 15px;
        }
        .plw-bet-title {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .plw-numbers {
            margin-top: 10px;
        }
        .plw-number-section {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        .plw-number-balls {
            display: flex;
            gap: 6px;
            flex-wrap: wrap;
        }
        .plw-number-ball {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 42px;
            height: 42px;
            border-radius: 50%;
            font-weight: bold;
            font-size: 18px;
            color: white;
            background-color: #67C23A;
            border: 3px solid #5daf34;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        /* 排列5每个位置使用不同的颜色 */
        .plw-number-ball.position-1 {
            background: linear-gradient(135deg, #FF6B6B, #FF5252);
            border-color: #f04747;
        }
        .plw-number-ball.position-2 {
            background: linear-gradient(135deg, #FFB74D, #FF9800);
            border-color: #FF9800;
        }
        .plw-number-ball.position-3 {
            background: linear-gradient(135deg, #4ECDC4, #26A69A);
            border-color: #26A69A;
        }
        .plw-number-ball.position-4 {
            background: linear-gradient(135deg, #45B7D1, #2196F3);
            border-color: #2196F3;
        }
        .plw-number-ball.position-5 {
            background: linear-gradient(135deg, #9C27B0, #673AB7);
            border-color: #673AB7;
        }

        /* 排列5位置显示样式 */
        .plw-position-numbers {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        .plw-position-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* 任选9相关样式 */
        .rx9-bet-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .rx9-bet-card {
            border: 1px solid #EBEEF5;
        }
        .rx9-bet-header {
            margin-bottom: 15px;
        }
        .rx9-bet-title {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .rx9-matches {
            margin-top: 10px;
        }
        .rx9-match-item {
            margin-bottom: 15px;
            padding: 12px;
            border: 1px solid #F0F0F0;
            border-radius: 6px;
            background: #FAFAFA;
        }
        .rx9-match-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
        }
        .rx9-match-num {
            background: #409EFF;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }
        .rx9-match-options {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }

        /* 6场半全场相关样式 */
        .bqc6-bet-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .bqc6-bet-card {
            border: 1px solid #EBEEF5;
        }
        .bqc6-bet-header {
            margin-bottom: 15px;
        }
        .bqc6-bet-title {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .bqc6-matches {
            margin-top: 10px;
        }
        .bqc6-match-item {
            margin-bottom: 15px;
            padding: 12px;
            border: 1px solid #F0F0F0;
            border-radius: 6px;
            background: #FAFAFA;
        }
        .bqc6-match-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
        }
        .bqc6-match-num {
            background: #E6A23C;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }
        .bqc6-match-options {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        .bqc6-half-time, .bqc6-full-time {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .time-label {
            font-weight: bold;
            color: #606266;
            min-width: 50px;
        }

        /* 4场进球相关样式 */
        .jq4-bet-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .jq4-bet-card {
            border: 1px solid #EBEEF5;
        }
        .jq4-bet-header {
            margin-bottom: 15px;
        }
        .jq4-bet-title {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .jq4-matches {
            margin-top: 10px;
        }
        .jq4-match-item {
            margin-bottom: 15px;
            padding: 12px;
            border: 1px solid #F0F0F0;
            border-radius: 6px;
            background: #FAFAFA;
        }
        .jq4-match-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
        }
        .jq4-match-num {
            background: #67C23A;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }
        .jq4-match-options {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        .jq4-goal-options {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .goal-label {
            font-weight: bold;
            color: #606266;
            min-width: 70px;
        }

        /* 胜负彩相关样式 */
        .sfc-bet-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .sfc-bet-card {
            border: 1px solid #EBEEF5;
        }
        .sfc-bet-header {
            margin-bottom: 15px;
        }
        .sfc-bet-title {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .sfc-matches {
            margin-top: 10px;
        }
        .sfc-match-item {
            margin-bottom: 15px;
            padding: 12px;
            border: 1px solid #F0F0F0;
            border-radius: 6px;
            background: #FAFAFA;
        }
        .sfc-match-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
        }
        .sfc-match-num {
            background: #722ED1;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }
        .sfc-match-options {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }
    </style>
    <script src="__CDN__/assets/addons/lottery/js/vue.mini.js"></script>
    <link rel="stylesheet" href="__CDN__/assets/addons/lottery/css/element.css">
    <script src="__CDN__/assets/addons/lottery/js/element.js"></script>
    <title>订单详情</title>
</head>
<body>
<div id="detail" v-cloak>
    <div class="order-detail-container">
        <!-- 基本信息 -->
        <el-card class="box-card" style="margin-bottom: 20px;">
            <div slot="header" class="clearfix">
                <span class="section-title">基本信息</span>
            </div>
            <el-descriptions :column="2" border>
                <el-descriptions-item label="订单ID">{{ order.id }}</el-descriptions-item>
                <el-descriptions-item label="订单号">{{ order.order_no }}</el-descriptions-item>
                <el-descriptions-item label="用户ID">{{ order.user_id }}</el-descriptions-item>
                <el-descriptions-item label="店铺ID">{{ order.store_id }}</el-descriptions-item>
                <el-descriptions-item label="彩票类型">
                    <el-tag type="primary">{{ order.lottery_type_text || order.lottery_type }}</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="订单状态">
                    <el-tag :type="getStatusType(order.status)">{{ getStatusText(order.status) }}</el-tag>
                </el-descriptions-item>
            </el-descriptions>
        </el-card>

        <!-- 投注信息 -->
        <el-card class="box-card" style="margin-bottom: 20px;">
            <div slot="header" class="clearfix">
                <span class="section-title">投注信息</span>
            </div>
            <el-descriptions :column="2" border>
                <el-descriptions-item label="投注注数">{{ order.bet_count }}</el-descriptions-item>
                <el-descriptions-item label="投注倍数">{{ order.bet_multiple }}</el-descriptions-item>
                <el-descriptions-item label="总金额">
                    <span class="amount-highlight">¥{{ formatAmount(order.total_amount) }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="实际金额">
                    <span class="amount-highlight">¥{{ formatAmount(order.amount) }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="潜在奖金">
                    <span class="amount-highlight">¥{{ formatAmount(order.potential_win) }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="中奖金额">
                    <span class="amount-highlight">¥{{ formatAmount(order.prize_amount) }}</span>
                </el-descriptions-item>
            </el-descriptions>
        </el-card>

        <!-- 状态和时间信息 -->
        <el-card class="box-card" style="margin-bottom: 20px;">
            <div slot="header" class="clearfix">
                <span class="section-title">状态和时间信息</span>
            </div>
            <el-descriptions :column="2" border>
                <el-descriptions-item label="合买状态">
                    <el-tag :type="getUnionStatusType(order.union_status)">{{ getUnionStatusText(order.union_status) }}</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="截止时间">{{ formatTime(order.deadline) }}</el-descriptions-item>
                <el-descriptions-item label="创建时间">{{ formatTime(order.createtime) }}</el-descriptions-item>
                <el-descriptions-item label="更新时间">{{ formatTime(order.updatetime) }}</el-descriptions-item>
                <el-descriptions-item label="删除时间" v-if="order.deletetime">{{ formatTime(order.deletetime) }}
                </el-descriptions-item>
            </el-descriptions>
        </el-card>

        <!-- 合买详情 -->
        <el-card class="box-card" v-if="order.union == 1 && order.union_info" style="margin-bottom: 20px;">
            <div slot="header" class="clearfix">
                <span class="section-title">合买详情</span>
            </div>

            <!-- 合买方案信息 -->
            <div style="margin-bottom: 20px;">
                <h4 style="margin-bottom: 15px; color: #303133;">合买方案信息</h4>
                <el-descriptions :column="2" border>
                    <el-descriptions-item label="方案ID">{{ order.union_info.plan.id }}</el-descriptions-item>
                    <el-descriptions-item label="方案状态">
                        <el-tag :type="getPlanStatusType(order.union_info.plan.status)">
                            {{ getPlanStatusText(order.union_info.plan.status) }}
                        </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="总份数">{{ order.union_info.plan.total_shares }}</el-descriptions-item>
                    <el-descriptions-item label="已认购份数">{{ order.union_info.plan.purchased_shares }}</el-descriptions-item>
                    <el-descriptions-item label="单份金额">
                        <span class="amount-highlight">¥{{ formatAmount(order.union_info.plan.unit_price) }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="认购进度">
                        <el-progress
                            :percentage="order.union_info.stats.progress_percent"
                            :color="getProgressColor(order.union_info.stats.progress_percent)">
                        </el-progress>
                    </el-descriptions-item>
                    <el-descriptions-item label="发起人份数">{{ order.union_info.plan.creator_shares }}</el-descriptions-item>
                    <el-descriptions-item label="保底份数">{{ order.union_info.plan.guarantee_shares }}</el-descriptions-item>
                    <el-descriptions-item label="佣金比例">{{ order.union_info.plan.commission_rate }}%</el-descriptions-item>
                    <el-descriptions-item label="可见性">
                        <el-tag :type="order.union_info.plan.visibility ? 'success' : 'warning'">
                            {{ order.union_info.plan.visibility ? '公开' : '私密' }}
                        </el-tag>
                    </el-descriptions-item>
                </el-descriptions>
            </div>

            <!-- 统计信息 -->
            <div style="margin-bottom: 20px;">
                <h4 style="margin-bottom: 15px; color: #303133;">统计信息</h4>
                <el-row :gutter="20">
                    <el-col :span="6">
                        <el-statistic title="总参与人数" :value="order.union_info.stats.total_participants"></el-statistic>
                    </el-col>
                    <el-col :span="6">
                        <el-statistic title="已支付人数" :value="order.union_info.stats.paid_participants"></el-statistic>
                    </el-col>
                    <el-col :span="6">
                        <el-statistic title="未支付人数" :value="order.union_info.stats.unpaid_participants"></el-statistic>
                    </el-col>
                    <el-col :span="6">
                        <el-statistic title="已收金额" :value="order.union_info.stats.total_paid_amount" :precision="2" prefix="¥"></el-statistic>
                    </el-col>
                </el-row>
            </div>

            <!-- 参与用户列表 -->
            <div>
                <h4 style="margin-bottom: 15px; color: #303133;">参与用户列表（已支付）</h4>
                <el-table :data="getPaidParticipations(order.union_info.participations)" border style="width: 100%">
                    <el-table-column prop="user_id" label="用户ID" width="80"></el-table-column>
                    <el-table-column label="用户信息" width="200">
                        <template slot-scope="scope">
                            <div>
                                <div>{{ scope.row.user_display }}</div>
                                <el-tag v-if="scope.row.is_creator" type="success" size="mini">发起人</el-tag>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="shares" label="认购份数" width="100"></el-table-column>
                    <el-table-column label="认购金额" width="120">
                        <template slot-scope="scope">
                            <span class="amount-highlight">¥{{ formatAmount(scope.row.amount) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="保底信息" width="150">
                        <template slot-scope="scope">
                            <div v-if="scope.row.is_guarantee">
                                <div>保底份数: {{ scope.row.guarantee_shares }}</div>
                                <div>保底金额: ¥{{ formatAmount(scope.row.guarantee_amount) }}</div>
                            </div>
                            <span v-else>-</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="总金额" width="120">
                        <template slot-scope="scope">
                            <span class="amount-highlight">¥{{ formatAmount(scope.row.total_amount) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="createtime_text" label="认购时间" width="160"></el-table-column>
                </el-table>
            </div>
        </el-card>

        <!-- 足球投注详情 -->
        <el-card class="box-card" v-if="order.lottery_type === 'football' && order.sub_order" style="margin-bottom: 20px;">
            <div slot="header" class="clearfix">
                <span class="section-title">足球投注详情</span>
            </div>
            <div>
                <!-- 投注基本信息 -->
                <el-descriptions :column="3" border style="margin-bottom: 20px;">
                    <el-descriptions-item label="投注类型">
                        <el-tag type="primary">{{ order.sub_order.bet_type_text || order.sub_order.bet_type }}</el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="投注方式">
                        <el-tag v-for="method in getBetMethods(order.sub_order.bet_method)" :key="method" style="margin-right: 5px;">
                            {{ method }}
                        </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="投注倍数">{{ order.sub_order.bet_multiple }}</el-descriptions-item>
                    <el-descriptions-item label="投注金额">
                        <span class="amount-highlight">¥{{ formatAmount(order.sub_order.amount) }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="比赛场次">{{ order.sub_order.match_ids }}</el-descriptions-item>
                    <el-descriptions-item label="截止时间">{{ formatTime(order.sub_order.deadline) }}</el-descriptions-item>
                </el-descriptions>

                <!-- 投注详情 -->
                <div class="bet-details">
                    <div class="bet-details-header">
                        <h4 style="margin-bottom: 15px; color: #303133;">投注选择详情</h4>
                        <el-button
                            v-if="canModifyBets(order.status)"
                            type="primary"
                            size="small"
                            icon="el-icon-edit"
                            @click="openModifyDialog">
                            修改
                        </el-button>
                    </div>
                    <div class="match-list">
                        <div v-for="(match, index) in getGroupedMatches(order.sub_order.bet_data)" :key="index" class="match-item">
                            <el-card class="match-card" shadow="hover">
                                <div class="match-header">
                                    <div class="match-title">
                                        <span class="match-num">{{ match.matchNum }}</span>
                                        <span class="team-name">{{ match.team1_name }}</span>
                                        <span class="vs">VS</span>
                                        <span class="team-name">{{ match.team2_name }}</span>
                                        <span class="match-day">{{ match.matchWeekDay }}</span>
                                    </div>
                                </div>
                                <div class="bet-options">
                                    <div v-for="option in match.options" :key="option.option_type" class="bet-option">
                                        <el-tag
                                            :type="getBetOptionType(option.option_type)"
                                            class="option-tag">
                                            {{ option.option_type }} @ {{ option.odds }}
                                        </el-tag>
                                    </div>
                                </div>
                            </el-card>
                        </div>
                    </div>
                </div>

                <!-- 修改赔率对话框 -->
                <el-dialog
                    title="修改投注赔率"
                    :visible.sync="modifyDialogVisible"
                    width="60%"
                    :before-close="handleModifyDialogClose">
                    <div class="modify-odds-content">
                        <div v-for="(match, matchIndex) in modifyBetData" :key="matchIndex" class="modify-match-item">
                            <div class="modify-match-header">
                                <h5>{{ match.matchNum }} {{ match.team1_name }} VS {{ match.team2_name }} {{ match.matchWeekDay }}</h5>
                            </div>
                            <div class="modify-options">
                                <div v-for="(option, optionIndex) in match.options" :key="optionIndex" class="modify-option-item">
                                    <div class="option-info">
                                        <el-tag :type="getBetOptionType(option.option_type)" size="small">
                                            {{ option.option_type }}
                                        </el-tag>
                                    </div>
                                    <div class="option-odds">
                                        <el-input-number
                                            v-model="option.odds"
                                            :precision="2"
                                            :step="0.01"
                                            :min="1.01"
                                            :max="999.99"
                                            size="small"
                                            style="width: 120px;">
                                        </el-input-number>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div slot="footer" class="dialog-footer">
                        <el-button @click="cancelModify">取 消</el-button>
                        <el-button type="primary" :loading="modifyLoading" @click="confirmModify">确 定</el-button>
                    </div>
                </el-dialog>
            </div>
        </el-card>

        <!-- 篮球投注详情 -->
        <el-card class="box-card" v-if="order.lottery_type === 'basketball' && order.sub_order" style="margin-bottom: 20px;">
            <div slot="header" class="clearfix">
                <span class="section-title">篮球投注详情</span>
            </div>
            <div>
                <!-- 投注基本信息 -->
                <el-descriptions :column="3" border style="margin-bottom: 20px;">
                    <el-descriptions-item label="投注类型">
                        <el-tag type="primary">{{ order.sub_order.bet_type_text || order.sub_order.bet_type }}</el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="投注方式">
                        <el-tag v-for="method in getBetMethods(order.sub_order.bet_method)" :key="method" style="margin-right: 5px;">
                            {{ method }}
                        </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="投注倍数">{{ order.sub_order.bet_multiple }}</el-descriptions-item>
                    <el-descriptions-item label="投注金额">
                        <span class="amount-highlight">¥{{ formatAmount(order.sub_order.amount) }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="比赛场次">{{ order.sub_order.match_ids }}</el-descriptions-item>
                    <el-descriptions-item label="截止时间">{{ formatTime(order.sub_order.deadline) }}</el-descriptions-item>
                </el-descriptions>

                <!-- 投注详情 -->
                <div class="bet-details">
                    <div class="bet-details-header">
                        <h4 style="margin-bottom: 15px; color: #303133;">投注选择详情</h4>
                        <el-button
                            v-if="canModifyBets(order.status)"
                            type="primary"
                            size="small"
                            icon="el-icon-edit"
                            @click="openBasketballModifyDialog">
                            修改
                        </el-button>
                    </div>
                    <div class="match-list">
                        <div v-for="(match, index) in getGroupedBasketballMatches(order.sub_order.bet_data)" :key="index" class="match-item">
                            <el-card class="match-card" shadow="hover">
                                <div class="match-header">
                                    <div class="match-title">
                                        <span class="match-num" v-if="match.matchNum">{{ match.matchNum }}</span>
                                        <span class="team-name">{{ match.team1_name }}</span>
                                        <span class="vs">VS</span>
                                        <span class="team-name">{{ match.team2_name }}</span>
                                        <span class="match-day" v-if="match.matchWeekDay">{{ match.matchWeekDay }}</span>
                                    </div>
                                </div>
                                <div class="bet-options">
                                    <div v-for="option in match.options" :key="option.option_type" class="bet-option">
                                        <el-tag
                                            :type="getBetOptionType(option.option_type)"
                                            class="option-tag">
                                            {{ option.option_type }} @ {{ option.odds }}
                                        </el-tag>
                                    </div>
                                </div>
                            </el-card>
                        </div>
                    </div>
                </div>

                <!-- 修改篮球赔率对话框 -->
                <el-dialog
                    title="修改篮球投注赔率"
                    :visible.sync="basketballModifyDialogVisible"
                    width="60%"
                    :before-close="handleBasketballModifyDialogClose">
                    <div class="modify-odds-content">
                        <div v-for="(match, matchIndex) in basketballModifyBetData" :key="matchIndex" class="modify-match-item">
                            <div class="modify-match-header">
                                <h5>
                                    <span v-if="match.matchNum">{{ match.matchNum }}</span>
                                    {{ match.team1_name }} VS {{ match.team2_name }}
                                    <span v-if="match.matchWeekDay">{{ match.matchWeekDay }}</span>
                                </h5>
                            </div>
                            <div class="modify-options">
                                <div v-for="(option, optionIndex) in match.options" :key="optionIndex" class="modify-option-item">
                                    <div class="option-info">
                                        <el-tag :type="getBetOptionType(option.option_type)" size="small">
                                            {{ option.option_type }}
                                        </el-tag>
                                    </div>
                                    <div class="option-odds">
                                        <el-input-number
                                            v-model="option.odds"
                                            :precision="2"
                                            :step="0.01"
                                            :min="1.01"
                                            :max="999.99"
                                            size="small"
                                            style="width: 120px;">
                                        </el-input-number>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div slot="footer" class="dialog-footer">
                        <el-button @click="cancelBasketballModify">取 消</el-button>
                        <el-button type="primary" :loading="basketballModifyLoading" @click="confirmBasketballModify">确 定</el-button>
                    </div>
                </el-dialog>
            </div>
        </el-card>

        <!-- 大乐透投注详情 -->
        <el-card class="box-card" v-if="order.lottery_type === 'dlt' && order.sub_order" style="margin-bottom: 20px;">
            <div slot="header" class="clearfix">
                <span class="section-title">大乐透投注详情</span>
            </div>
            <div>
                <!-- 投注基本信息 -->
                <el-descriptions :column="3" border style="margin-bottom: 20px;">
                    <el-descriptions-item label="投注类型">
                        <el-tag type="primary">{{ getDltBetTypeText(order.sub_order) }}</el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="投注注数">{{ order.bet_count }}</el-descriptions-item>
                    <el-descriptions-item label="投注倍数">{{ order.bet_multiple }}</el-descriptions-item>
                    <el-descriptions-item label="投注金额">
                        <span class="amount-highlight">¥{{ formatAmount(order.total_amount) }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="期号">{{ getDltIssueNumber(order.sub_order) }}</el-descriptions-item>
                    <el-descriptions-item label="截止时间">{{ formatTime(order.deadline) }}</el-descriptions-item>
                </el-descriptions>

                <!-- 投注详情 -->
                <div class="bet-details">
                    <div class="bet-details-header">
                        <h4 style="margin-bottom: 15px; color: #303133;">投注号码详情</h4>
                    </div>
                    <div class="dlt-bet-list">
                        <div v-for="(dltOrder, index) in getDltOrderList(order.sub_order)" :key="index" class="dlt-bet-item">
                            <el-card class="dlt-bet-card" shadow="hover">
                                <div class="dlt-bet-header">
                                    <div class="dlt-bet-title">
                                        <span class="bet-index">第{{ index + 1 }}注</span>
                                        <span class="bet-type-tag">
                                            <el-tag size="small" type="info">{{ dltOrder.bet_type_text }}</el-tag>
                                        </span>
                                        <span class="bet-multiple">{{ dltOrder.bet_multiple }}倍</span>
                                    </div>
                                </div>
                                <div class="dlt-numbers">
                                    <!-- 显示bet_data格式的号码 -->
                                    <div v-if="dltOrder.bet_data" class="bet-data-numbers">
                                        <div class="number-section">
                                            <span class="section-label">前区：</span>
                                            <div class="number-balls front-balls">
                                                <span
                                                    v-for="num in dltOrder.bet_data.front"
                                                    :key="'front-' + num"
                                                    class="number-ball front-ball">
                                                    {{ num }}
                                                </span>
                                            </div>
                                        </div>
                                        <div class="number-section">
                                            <span class="section-label">后区：</span>
                                            <div class="number-balls back-balls">
                                                <span
                                                    v-for="num in dltOrder.bet_data.back"
                                                    :key="'back-' + num"
                                                    class="number-ball back-ball">
                                                    {{ num }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </el-card>
                        </div>
                    </div>
                </div>
            </div>
        </el-card>

        <!-- 七星彩投注详情 -->
        <el-card class="box-card" v-if="order.lottery_type === 'qxc' && order.sub_order" style="margin-bottom: 20px;">
            <div slot="header" class="clearfix">
                <span class="section-title">七星彩投注详情</span>
            </div>
            <div>
                <!-- 投注基本信息 -->
                <el-descriptions :column="3" border style="margin-bottom: 20px;">
                    <el-descriptions-item label="投注类型">
                        <el-tag type="primary">{{ getQxcBetTypeText(order.sub_order) }}</el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="投注注数">{{ order.bet_count }}</el-descriptions-item>
                    <el-descriptions-item label="投注倍数">{{ order.bet_multiple }}</el-descriptions-item>
                    <el-descriptions-item label="投注金额">
                        <span class="amount-highlight">¥{{ formatAmount(order.total_amount) }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="期号">{{ getQxcIssueNumber(order.sub_order) }}</el-descriptions-item>
                    <el-descriptions-item label="截止时间">{{ formatTime(order.deadline) }}</el-descriptions-item>
                </el-descriptions>

                <!-- 投注详情 -->
                <div class="bet-details">
                    <div class="bet-details-header">
                        <h4 style="margin-bottom: 15px; color: #303133;">投注号码详情</h4>
                    </div>
                    <div class="qxc-bet-list">
                        <div v-for="(qxcOrder, index) in getQxcOrderList(order.sub_order)" :key="index" class="qxc-bet-item">
                            <el-card class="qxc-bet-card" shadow="hover">
                                <div class="qxc-bet-header">
                                    <div class="qxc-bet-title">
                                        <span class="bet-index">第{{ index + 1 }}注</span>
                                        <span class="bet-type-tag">
                                            <el-tag size="small" type="info">{{ qxcOrder.bet_type_text || '单式投注' }}</el-tag>
                                        </span>
                                        <span class="bet-multiple">{{ qxcOrder.bet_multiple }}倍</span>
                                    </div>
                                </div>
                                <div class="qxc-numbers">
                                    <!-- 显示bet_data格式的号码 -->
                                    <div v-if="qxcOrder.bet_data" class="qxc-bet-data-numbers">
                                        <div class="qxc-number-section">
                                            <span class="section-label">号码：</span>
                                            <div class="qxc-number-balls">
                                                <span
                                                    v-for="(num, position) in getQxcNumbers(qxcOrder.bet_data)"
                                                    :key="'pos-' + position"
                                                    class="qxc-number-ball"
                                                    :class="'position-' + (position + 1)">
                                                    {{ num }}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </el-card>
                        </div>
                    </div>
                </div>
            </div>
        </el-card>

        <!-- 排列三投注详情 -->
        <el-card class="box-card" v-if="order.lottery_type === 'pls' && order.sub_order" style="margin-bottom: 20px;">
            <div slot="header" class="clearfix">
                <span class="section-title">排列三投注详情</span>
            </div>
            <div>
                <!-- 投注基本信息 -->
                <el-descriptions :column="3" border style="margin-bottom: 20px;">
                    <el-descriptions-item label="投注类型">
                        <el-tag type="primary">{{ getPlsBetTypeText(order.sub_order) }}</el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="投注注数">{{ order.bet_count }}</el-descriptions-item>
                    <el-descriptions-item label="投注倍数">{{ order.bet_multiple }}</el-descriptions-item>
                    <el-descriptions-item label="投注金额">
                        <span class="amount-highlight">¥{{ formatAmount(order.total_amount) }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="期号">{{ getPlsIssueNumber(order.sub_order) }}</el-descriptions-item>
                    <el-descriptions-item label="截止时间">{{ formatTime(order.deadline) }}</el-descriptions-item>
                </el-descriptions>

                <!-- 投注详情 -->
                <div class="bet-details">
                    <div class="bet-details-header">
                        <h4 style="margin-bottom: 15px; color: #303133;">投注号码详情</h4>
                    </div>
                    <div class="pls-bet-list">
                        <div v-for="(plsOrder, index) in getPlsOrderList(order.sub_order)" :key="index" class="pls-bet-item">
                            <el-card class="pls-bet-card" shadow="hover">
                                <div class="pls-bet-header">
                                    <div class="pls-bet-title">
                                        <span class="bet-index">第{{ index + 1 }}注</span>
                                        <span class="bet-type-tag">
                                            <el-tag size="small" type="info">{{ plsOrder.bet_type_text || '单式投注' }}</el-tag>
                                        </span>
                                        <span class="bet-multiple">{{ plsOrder.bet_multiple }}倍</span>
                                    </div>
                                </div>
                                <div class="pls-numbers">
                                    <!-- 显示bet_data格式的号码 -->
                                    <div v-if="plsOrder.bet_data" class="pls-bet-data-numbers">
                                        <div class="pls-number-section">
                                            <span class="section-label">投注号码：</span>
                                            <div class="pls-position-numbers">
                                                <div class="pls-position-item">
                                                    <span class="position-label">百位：</span>
                                                    <div class="pls-number-balls">
                                                        <span v-for="number in getPlsPositionNumbers(plsOrder.bet_data, 'bai')"
                                                              :key="'bai-' + number"
                                                              class="pls-number-ball position-1">
                                                            {{ number }}
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="pls-position-item">
                                                    <span class="position-label">十位：</span>
                                                    <div class="pls-number-balls">
                                                        <span v-for="number in getPlsPositionNumbers(plsOrder.bet_data, 'shi')"
                                                              :key="'shi-' + number"
                                                              class="pls-number-ball position-2">
                                                            {{ number }}
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="pls-position-item">
                                                    <span class="position-label">个位：</span>
                                                    <div class="pls-number-balls">
                                                        <span v-for="number in getPlsPositionNumbers(plsOrder.bet_data, 'ge')"
                                                              :key="'ge-' + number"
                                                              class="pls-number-ball position-3">
                                                            {{ number }}
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </el-card>
                        </div>
                    </div>
                </div>
            </div>
        </el-card>

        <!-- 排列5投注详情 -->
        <el-card class="box-card" v-if="order.lottery_type === 'plw' && order.sub_order" style="margin-bottom: 20px;">
            <div slot="header" class="clearfix">
                <span class="section-title">排列5投注详情</span>
            </div>
            <div>
                <!-- 投注基本信息 -->
                <el-descriptions :column="3" border style="margin-bottom: 20px;">
                    <el-descriptions-item label="投注类型">
                        <el-tag type="primary">{{ getPlwBetTypeText(order.sub_order) }}</el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="投注注数">{{ order.bet_count }}</el-descriptions-item>
                    <el-descriptions-item label="投注倍数">{{ order.bet_multiple }}</el-descriptions-item>
                    <el-descriptions-item label="投注金额">
                        <span class="amount-highlight">¥{{ formatAmount(order.total_amount) }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="期号">{{ getPlwIssueNumber(order.sub_order) }}</el-descriptions-item>
                    <el-descriptions-item label="截止时间">{{ formatTime(order.deadline) }}</el-descriptions-item>
                </el-descriptions>

                <!-- 投注详情 -->
                <div class="bet-details">
                    <div class="bet-details-header">
                        <h4 style="margin-bottom: 15px; color: #303133;">投注号码详情</h4>
                    </div>
                    <div class="plw-bet-list">
                        <div v-for="(plwOrder, index) in getPlwOrderList(order.sub_order)" :key="index" class="plw-bet-item">
                            <el-card class="plw-bet-card" shadow="hover">
                                <div class="plw-bet-header">
                                    <div class="plw-bet-title">
                                        <span class="bet-index">第{{ index + 1 }}注</span>
                                        <span class="bet-type-tag">
                                            <el-tag size="small" type="info">{{ plwOrder.bet_type_text || '单式投注' }}</el-tag>
                                        </span>
                                        <span class="bet-multiple">{{ plwOrder.bet_multiple }}倍</span>
                                    </div>
                                </div>
                                <div class="plw-numbers">
                                    <!-- 显示bet_data格式的号码 -->
                                    <div v-if="plwOrder.bet_data" class="plw-bet-data-numbers">
                                        <div class="plw-number-section">
                                            <span class="section-label">投注号码：</span>
                                            <div class="plw-position-numbers">
                                                <div class="plw-position-item">
                                                    <span class="position-label">万位：</span>
                                                    <div class="plw-number-balls">
                                                        <span v-for="number in getPlwPositionNumbers(plwOrder.bet_data, 'wan')"
                                                              :key="'wan-' + number"
                                                              class="plw-number-ball position-1">
                                                            {{ number }}
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="plw-position-item">
                                                    <span class="position-label">千位：</span>
                                                    <div class="plw-number-balls">
                                                        <span v-for="number in getPlwPositionNumbers(plwOrder.bet_data, 'qian')"
                                                              :key="'qian-' + number"
                                                              class="plw-number-ball position-2">
                                                            {{ number }}
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="plw-position-item">
                                                    <span class="position-label">百位：</span>
                                                    <div class="plw-number-balls">
                                                        <span v-for="number in getPlwPositionNumbers(plwOrder.bet_data, 'bai')"
                                                              :key="'bai-' + number"
                                                              class="plw-number-ball position-3">
                                                            {{ number }}
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="plw-position-item">
                                                    <span class="position-label">十位：</span>
                                                    <div class="plw-number-balls">
                                                        <span v-for="number in getPlwPositionNumbers(plwOrder.bet_data, 'shi')"
                                                              :key="'shi-' + number"
                                                              class="plw-number-ball position-4">
                                                            {{ number }}
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="plw-position-item">
                                                    <span class="position-label">个位：</span>
                                                    <div class="plw-number-balls">
                                                        <span v-for="number in getPlwPositionNumbers(plwOrder.bet_data, 'ge')"
                                                              :key="'ge-' + number"
                                                              class="plw-number-ball position-5">
                                                            {{ number }}
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </el-card>
                        </div>
                    </div>
                </div>
            </div>
        </el-card>

        <!-- 任选9投注详情 -->
        <el-card class="box-card" v-if="order.lottery_type === 'rx9' && order.sub_order" style="margin-bottom: 20px;">
            <div slot="header" class="clearfix">
                <span class="section-title">任选9投注详情</span>
            </div>
            <div>
                <!-- 投注基本信息 -->
                <el-descriptions :column="3" border style="margin-bottom: 20px;">
                    <el-descriptions-item label="投注类型">
                        <el-tag type="primary">任选9</el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="投注注数">{{ order.bet_count }}</el-descriptions-item>
                    <el-descriptions-item label="投注倍数">{{ order.bet_multiple }}</el-descriptions-item>
                    <el-descriptions-item label="投注金额">
                        <span class="amount-highlight">¥{{ formatAmount(order.total_amount) }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="期号">{{ getRx9IssueNumber(order.sub_order) }}</el-descriptions-item>
                    <el-descriptions-item label="截止时间">{{ formatTime(order.deadline) }}</el-descriptions-item>
                </el-descriptions>

                <!-- 投注详情 -->
                <div class="bet-details">
                    <div class="bet-details-header">
                        <h4 style="margin-bottom: 15px; color: #303133;">投注选择详情</h4>
                    </div>
                    <div class="rx9-bet-list">
                        <div v-for="(rx9Order, index) in getRx9OrderList(order.sub_order)" :key="index" class="rx9-bet-item">
                            <el-card class="rx9-bet-card" shadow="hover">
                                <div class="rx9-bet-header">
                                    <div class="rx9-bet-title">
                                        <span class="bet-index">第{{ index + 1 }}注</span>
                                        <span class="bet-multiple">{{ rx9Order.bet_multiple }}倍</span>
                                    </div>
                                </div>
                                <div class="rx9-matches">
                                    <div v-for="(match, matchIndex) in getRx9GroupedMatches(rx9Order.bet_data)" :key="matchIndex" class="rx9-match-item">
                                        <div class="rx9-match-header">
                                            <span class="rx9-match-num">{{ match.match_id }}</span>
                                            <span class="team-name">{{ match.team1_name }}</span>
                                            <span class="vs">VS</span>
                                            <span class="team-name">{{ match.team2_name }}</span>
                                        </div>
                                        <div class="rx9-match-options">
                                            <el-tag
                                                v-for="option in match.options"
                                                :key="option.option_type"
                                                :type="getBetOptionType(option.option_type)"
                                                size="small"
                                                style="margin-right: 5px;">
                                                {{ option.option_type }} @ {{ option.odds }}
                                            </el-tag>
                                        </div>
                                    </div>
                                </div>
                            </el-card>
                        </div>
                    </div>
                                </div>
            </div>
        </el-card>

        <!-- 6场半全场投注详情 -->
        <el-card class="box-card" v-if="order.lottery_type === 'bqc6' && order.sub_order" style="margin-bottom: 20px;">
            <div slot="header" class="clearfix">
                <span class="section-title">6场半全场投注详情</span>
            </div>
            <div>
                <!-- 投注基本信息 -->
                <el-descriptions :column="3" border style="margin-bottom: 20px;">
                    <el-descriptions-item label="投注类型">
                        <el-tag type="primary">6场半全场</el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="投注注数">{{ order.bet_count }}</el-descriptions-item>
                    <el-descriptions-item label="投注倍数">{{ order.bet_multiple }}</el-descriptions-item>
                    <el-descriptions-item label="投注金额">
                        <span class="amount-highlight">¥{{ formatAmount(order.total_amount) }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="期号">{{ getBqc6IssueNumber(order.sub_order) }}</el-descriptions-item>
                    <el-descriptions-item label="截止时间">{{ formatTime(order.deadline) }}</el-descriptions-item>
                </el-descriptions>

                <!-- 投注详情 -->
                <div class="bet-details">
                    <div class="bet-details-header">
                        <h4 style="margin-bottom: 15px; color: #303133;">投注选择详情</h4>
                    </div>
                    <div class="bqc6-bet-list">
                        <div v-for="(bqc6Order, index) in getBqc6OrderList(order.sub_order)" :key="index" class="bqc6-bet-item">
                            <el-card class="bqc6-bet-card" shadow="hover">
                                <div class="bqc6-bet-header">
                                    <div class="bqc6-bet-title">
                                        <span class="bet-index">第{{ index + 1 }}注</span>
                                        <span class="bet-multiple">{{ bqc6Order.bet_multiple }}倍</span>
                                    </div>
                                </div>
                                <div class="bqc6-matches">
                                    <div v-for="(match, matchIndex) in getBqc6GroupedMatches(bqc6Order.bet_data)" :key="matchIndex" class="bqc6-match-item">
                                        <div class="bqc6-match-header">
                                            <span class="bqc6-match-num">{{ match.match_id }}</span>
                                            <span class="team-name">{{ match.team1_name }}</span>
                                            <span class="vs">VS</span>
                                            <span class="team-name">{{ match.team2_name }}</span>
                                        </div>
                                        <div class="bqc6-match-options">
                                            <div class="bqc6-half-time">
                                                <span class="time-label">半场：</span>
                                                <el-tag
                                                    v-for="option in match.half_options"
                                                    :key="option.option_type"
                                                    type="warning"
                                                    size="small"
                                                    style="margin-right: 5px;">
                                                    {{ getBqc6OptionText(option.option_type) }}
                                                </el-tag>
                                            </div>
                                            <div class="bqc6-full-time">
                                                <span class="time-label">全场：</span>
                                                <el-tag
                                                    v-for="option in match.full_options"
                                                    :key="option.option_type"
                                                    type="success"
                                                    size="small"
                                                    style="margin-right: 5px;">
                                                    {{ getBqc6OptionText(option.option_type) }}
                                                </el-tag>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </el-card>
                        </div>
                    </div>
                </div>
            </div>
        </el-card>

        <!-- 4场进球投注详情 -->
        <el-card class="box-card" v-if="order.lottery_type === 'jq4' && order.sub_order" style="margin-bottom: 20px;">
            <div slot="header" class="clearfix">
                <span class="section-title">4场进球投注详情</span>
            </div>
            <div>
                <!-- 投注基本信息 -->
                <el-descriptions :column="3" border style="margin-bottom: 20px;">
                    <el-descriptions-item label="投注类型">
                        <el-tag type="primary">4场进球</el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="投注注数">{{ order.bet_count }}</el-descriptions-item>
                    <el-descriptions-item label="投注倍数">{{ order.bet_multiple }}</el-descriptions-item>
                    <el-descriptions-item label="投注金额">
                        <span class="amount-highlight">¥{{ formatAmount(order.total_amount) }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="期号">{{ getJq4IssueNumber(order.sub_order) }}</el-descriptions-item>
                    <el-descriptions-item label="截止时间">{{ formatTime(order.deadline) }}</el-descriptions-item>
                </el-descriptions>

                <!-- 投注详情 -->
                <div class="bet-details">
                    <div class="bet-details-header">
                        <h4 style="margin-bottom: 15px; color: #303133;">投注选择详情</h4>
                    </div>
                    <div class="jq4-bet-list">
                        <div v-for="(jq4Order, index) in getJq4OrderList(order.sub_order)" :key="index" class="jq4-bet-item">
                            <el-card class="jq4-bet-card" shadow="hover">
                                <div class="jq4-bet-header">
                                    <div class="jq4-bet-title">
                                        <span class="bet-index">第{{ index + 1 }}注</span>
                                        <span class="bet-multiple">{{ jq4Order.bet_multiple }}倍</span>
                                    </div>
                                </div>
                                <div class="jq4-matches">
                                    <div v-for="(match, matchIndex) in getJq4GroupedMatches(jq4Order.bet_data)" :key="matchIndex" class="jq4-match-item">
                                        <div class="jq4-match-header">
                                            <span class="jq4-match-num">{{ match.match_id }}</span>
                                            <span class="team-name">{{ match.team1_name }}</span>
                                            <span class="vs">VS</span>
                                            <span class="team-name">{{ match.team2_name }}</span>
                                        </div>
                                        <div class="jq4-match-options">
                                            <div class="jq4-goal-options">
                                                <span class="goal-label">主队进球：</span>
                                                <el-tag
                                                    v-for="option in match.home_options"
                                                    :key="option.option_type"
                                                    type="primary"
                                                    size="small"
                                                    style="margin-right: 5px;">
                                                    {{ getJq4OptionText(option.option_type) }}
                                                </el-tag>
                                            </div>
                                            <div class="jq4-goal-options">
                                                <span class="goal-label">客队进球：</span>
                                                <el-tag
                                                    v-for="option in match.away_options"
                                                    :key="option.option_type"
                                                    type="danger"
                                                    size="small"
                                                    style="margin-right: 5px;">
                                                    {{ getJq4OptionText(option.option_type) }}
                                                </el-tag>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </el-card>
                        </div>
                    </div>
                </div>
            </div>
        </el-card>

        <!-- 胜负彩投注详情 -->
        <el-card class="box-card" v-if="order.lottery_type === 'sfc' && order.sub_order" style="margin-bottom: 20px;">
            <div slot="header" class="clearfix">
                <span class="section-title">胜负彩投注详情</span>
            </div>
            <div>
                <!-- 投注基本信息 -->
                <el-descriptions :column="3" border style="margin-bottom: 20px;">
                    <el-descriptions-item label="投注类型">
                        <el-tag type="primary">胜负彩</el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="投注注数">{{ order.bet_count }}</el-descriptions-item>
                    <el-descriptions-item label="投注倍数">{{ order.bet_multiple }}</el-descriptions-item>
                    <el-descriptions-item label="投注金额">
                        <span class="amount-highlight">¥{{ formatAmount(order.total_amount) }}</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="期号">{{ getSfcIssueNumber(order.sub_order) }}</el-descriptions-item>
                    <el-descriptions-item label="截止时间">{{ formatTime(order.deadline) }}</el-descriptions-item>
                </el-descriptions>

                <!-- 投注详情 -->
                <div class="bet-details">
                    <div class="bet-details-header">
                        <h4 style="margin-bottom: 15px; color: #303133;">投注选择详情</h4>
                    </div>
                    <div class="sfc-bet-list">
                        <div v-for="(sfcOrder, index) in getSfcOrderList(order.sub_order)" :key="index" class="sfc-bet-item">
                            <el-card class="sfc-bet-card" shadow="hover">
                                <div class="sfc-bet-header">
                                    <div class="sfc-bet-title">
                                        <span class="bet-index">第{{ index + 1 }}注</span>
                                        <span class="bet-multiple">{{ sfcOrder.bet_multiple }}倍</span>
                                    </div>
                                </div>
                                <div class="sfc-matches">
                                    <div v-for="(match, matchIndex) in getSfcGroupedMatches(sfcOrder.bet_data)" :key="matchIndex" class="sfc-match-item">
                                        <div class="sfc-match-header">
                                            <span class="sfc-match-num">{{ match.match_id }}</span>
                                            <span class="team-name">{{ match.team1_name }}</span>
                                            <span class="vs">VS</span>
                                            <span class="team-name">{{ match.team2_name }}</span>
                                        </div>
                                        <div class="sfc-match-options">
                                            <el-tag
                                                v-for="option in match.options"
                                                :key="option.option_type"
                                                :type="getBetOptionType(option.option_type)"
                                                size="small"
                                                style="margin-right: 5px;">
                                                {{ option.option_type }}
                                            </el-tag>
                                        </div>
                                    </div>
                                </div>
                            </el-card>
                        </div>
                    </div>
                </div>
            </div>
        </el-card>

        <!-- 出票操作 -->
        <el-card class="box-card" v-if="order.status == 1" style="margin-bottom: 20px;">
            <div slot="header" class="clearfix">
                <span class="section-title">出票操作</span>
            </div>
            <div class="upload-section">
                <!-- 选择图片按钮 -->
                <div style="margin-bottom: 15px;">
                    <el-button
                        type="primary"
                        icon="el-icon-picture"
                        @click="selectImages">
                        选择票据图片
                    </el-button>
                    <span class="upload-tip" style="margin-left: 10px;">
                        可选择多张图片作为票据
                    </span>
                </div>

                <!-- 已选择的图片预览 -->
                <div v-if="ticketImages.length > 0" class="selected-images">
                    <div class="image-preview-list">
                        <div
                            v-for="(image, index) in ticketImages"
                            :key="index"
                            class="image-preview-item">
                            <el-image
                                :src="image"
                                :preview-src-list="ticketImages"
                                class="preview-image"
                                fit="cover">
                                <div slot="error" class="image-slot">
                                    <i class="el-icon-picture-outline"></i>
                                </div>
                            </el-image>
                            <div class="image-actions">
                                <el-button
                                    type="danger"
                                    size="mini"
                                    icon="el-icon-delete"
                                    @click="removeImage(index)">
                                </el-button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="ticket-actions">
                    <el-button
                        type="primary"
                        size="medium"
                        :loading="ticketLoading"
                        :disabled="ticketImages.length === 0"
                        @click="submitTicket">
                        <i class="el-icon-check"></i> 确认出票
                    </el-button>
                    <el-button
                        size="medium"
                        @click="cancelTicket">
                        <i class="el-icon-close"></i> 取消
                    </el-button>
                </div>
            </div>
        </el-card>

        <!-- 票据图片 -->
        <el-card class="box-card" v-if="order.ticket_images && order.ticket_images.length > 0">
            <div slot="header" class="clearfix">
                <span class="section-title">票据图片</span>
            </div>
            <div>
                <el-image
                        v-for="(image, index) in order.ticket_images"
                        :key="index"
                        :src="image"
                        :preview-src-list="order.ticket_images"
                        class="ticket-image"
                        fit="cover">
                    <div slot="error" class="image-slot">
                        <i class="el-icon-picture-outline"></i>
                    </div>
                </el-image>
            </div>

        </el-card>
    </div>
</div>


</body>
</html>