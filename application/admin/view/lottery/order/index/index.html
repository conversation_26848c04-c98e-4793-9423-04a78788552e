<div class="panel panel-default panel-intro">

    <div class="panel-heading">
        {:build_heading(null,FALSE)}
        <ul class="nav nav-tabs " data-field="status">
            <li class="active"><a href="#t-all" data-value="" data-toggle="tab">{:__('All')}</a></li>
            {foreach name="statusList" item="vo"}
            <li><a href="#t-{$key}" data-value="{$key}" data-toggle="tab">{$vo}</a></li>
            {/foreach}
        </ul>

        <ul class="nav nav-tabs " data-field="lottery_type">
            <li class="active"><a href="#t-all" data-value="" data-toggle="tab">{:__('All')}</a></li>
            {foreach name="lotteryTypeList" item="vo"}
            <li><a href="#t-{$key}" data-value="{$key}" data-toggle="tab">{$vo}</a></li>
            {/foreach}
        </ul>

        <ul class="nav nav-tabs " data-field="union">
            <li class="active"><a href="#t-all" data-value="" data-toggle="tab">{:__('All')}</a></li>
            {foreach name="unionList" item="vo" key="key"}
            <li><a href="#t-union-{$key}" data-value="{$key}" data-toggle="tab">{$vo}</a></li>
            {/foreach}
        </ul>

        <ul class="nav nav-tabs " data-field="union_status" >
            <li class="active"><a href="#t-all-status" data-value="" data-toggle="tab">{:__('All')}</a></li>
            {foreach name="unionStatusList" item="vo" key="key"}
            <li><a href="#t-union-status-{$key}" data-value="{$key}" data-toggle="tab">{$vo}</a></li>
            {/foreach}
        </ul>

    </div>

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        <a href="javascript:;" class="btn btn-primary btn-refresh" title="{:__('Refresh')}"><i
                                class="fa fa-refresh"></i> </a>
                        <a href="javascript:;"
                           class="btn btn-success btn-add {:$auth->check('lottery/order/index/add')?'':'hide'}"
                           title="{:__('Add')}"><i class="fa fa-plus"></i> {:__('Add')}</a>
                        <a href="javascript:;"
                           class="btn btn-success btn-edit btn-disabled disabled {:$auth->check('lottery/order/index/edit')?'':'hide'}"
                           title="{:__('Edit')}"><i class="fa fa-pencil"></i> {:__('Edit')}</a>
                        <a href="javascript:;"
                           class="btn btn-danger btn-del btn-disabled disabled {:$auth->check('lottery/order/index/del')?'':'hide'}"
                           title="{:__('Delete')}"><i class="fa fa-trash"></i> {:__('Delete')}</a>

                        <a href="javascript:;"
                           class="btn btn-success btn-batch-award btn-disabled disabled {:$auth->check('lottery/order/index/award')?'':'hide'}"
                           title="批量派奖"><i class="fa fa-money"></i> 批量派奖</a>

                        <a href="javascript:;"
                           class="btn btn-warning btn-batch-refund btn-disabled disabled {:$auth->check('lottery/order/index/refund')?'':'hide'}"
                           title="批量退单"><i class="fa fa-undo"></i> 批量退单</a>

                        <a class="btn btn-success btn-recyclebin btn-dialog {:$auth->check('lottery/order/index/recyclebin')?'':'hide'}"
                           href="lottery/order/index/recyclebin" title="{:__('Recycle bin')}"><i
                                class="fa fa-recycle"></i> {:__('Recycle bin')}</a>
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover table-nowrap"
                           data-operate-edit="{:$auth->check('lottery/order/index/edit')}"
                           data-operate-del="{:$auth->check('lottery/order/index/del')}"
                           data-operate-award="{:$auth->check('lottery/order/index/award')}"
                           data-operate-refund="{:$auth->check('lottery/order/index/refund')}">
                    </table>
                </div>
            </div>

        </div>
    </div>
</div>