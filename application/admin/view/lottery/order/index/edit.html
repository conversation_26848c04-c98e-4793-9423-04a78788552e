<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_id" data-rule="required" data-source="user/user/index" data-field="nickname" class="form-control selectpage" name="row[user_id]" type="text" value="{$row.user_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Store_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-store_id" data-rule="required" data-source="store/index" class="form-control selectpage" name="row[store_id]" type="text" value="{$row.store_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Order_no')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-order_no" class="form-control" name="row[order_no]" type="text" value="{$row.order_no|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Lottery_type')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-lottery_type" class="form-control selectpicker" name="row[lottery_type]">
                {foreach name="lotteryTypeList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.lottery_type"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Bet_count')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-bet_count" data-rule="required" class="form-control" name="row[bet_count]" type="number" value="{$row.bet_count|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Bet_multiple')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-bet_multiple" data-rule="required" class="form-control" name="row[bet_multiple]" type="number" value="{$row.bet_multiple|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Total_amount')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-total_amount" data-rule="required" class="form-control" step="0.01" name="row[total_amount]" type="number" value="{$row.total_amount|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Amount')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-amount" data-rule="required" class="form-control" step="0.01" name="row[amount]" type="number" value="{$row.amount|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Potential_win')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-potential_win" data-rule="required" class="form-control" step="0.01" name="row[potential_win]" type="number" value="{$row.potential_win|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Prize_amount')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-prize_amount" class="form-control" step="0.01" name="row[prize_amount]" type="number" value="{$row.prize_amount|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-status" data-rule="required" class="form-control" name="row[status]" type="number" value="{$row.status|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Union')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-union" class="form-control" name="row[union]" type="number" value="{$row.union|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Deadline')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-deadline" min="0" class="form-control" name="row[deadline]" type="number" value="{$row.deadline|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Ticket_images')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-ticket_images" class="form-control" size="50" name="row[ticket_images]" type="textarea" value="{$row.ticket_images|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-ticket_images" class="btn btn-danger faupload" data-input-id="c-ticket_images" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="true" data-preview-id="p-ticket_images"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-ticket_images" class="btn btn-primary fachoose" data-input-id="c-ticket_images" data-mimetype="image/*" data-multiple="true"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-ticket_images"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-ticket_images"></ul>
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>
