<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="">
    <style>
        [v-cloak] {
            display: none !important;
        }

        .award-container {
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }

        .section-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #303133;
        }

        .amount-highlight {
            color: #E6A23C;
            font-weight: bold;
        }

        .status-success {
            color: #67C23A;
        }

        .status-warning {
            color: #E6A23C;
        }

        .status-danger {
            color: #F56C6C;
        }

        .status-info {
            color: #409EFF;
        }

        .award-form {
            margin-top: 20px;
        }

        .form-section {
            margin-bottom: 30px;
        }

        .actual-amount {
            font-size: 18px;
            font-weight: bold;
            color: #67C23A;
        }

        .actual-amount.negative {
            color: #F56C6C;
        }

        .form-buttons {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #EBEEF5;
        }
    </style>
    <script src="__CDN__/assets/addons/lottery/js/vue.mini.js"></script>
    <link rel="stylesheet" href="__CDN__/assets/addons/lottery/css/element.css">
    <script src="__CDN__/assets/addons/lottery/js/element.js"></script>
    <title>派奖 - {$order.order_no}</title>
</head>
<body>
<div id="award" v-cloak>
    <div class="award-container">
        <!-- 订单基本信息 -->
        <el-card class="box-card" style="margin-bottom: 20px;">
            <div slot="header" class="clearfix">
                <span class="section-title">订单信息</span>
            </div>
            <el-descriptions :column="2" border>
                <el-descriptions-item label="订单号">{{ order.order_no }}</el-descriptions-item>
                <el-descriptions-item label="用户">{{ user.username }} ({{ user.mobile || '未绑定手机' }})</el-descriptions-item>
                <el-descriptions-item label="彩种类型">
                    <el-tag type="primary">{{ order.lottery_type_text || order.lottery_type }}</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="订单状态">
                    <el-tag :type="getStatusType(order.status)">{{ getStatusText(order.status) }}</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="投注金额">
                    <span class="amount-highlight">¥{{ formatAmount(order.total_amount) }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="用户当前余额">
                    <span class="amount-highlight">¥{{ formatAmount(user.money) }}</span>
                </el-descriptions-item>
            </el-descriptions>
        </el-card>

        <!-- 奖金信息 -->
        <el-card class="box-card" style="margin-bottom: 20px;">
            <div slot="header" class="clearfix">
                <span class="section-title">奖金信息</span>
            </div>
            <el-descriptions :column="1" border>
                <el-descriptions-item label="已记录奖金">
                    <span class="amount-highlight">¥{{ formatAmount(order.prize_amount) }}</span>
                    <el-tooltip content="当前记录的中奖金额" placement="top">
                        <i class="el-icon-question" style="margin-left: 5px; color: #909399;"></i>
                    </el-tooltip>
                </el-descriptions-item>
            </el-descriptions>
        </el-card>

        <!-- 派奖表单 -->
        <el-card class="box-card award-form">
            <div slot="header" class="clearfix">
                <span class="section-title">派奖设置</span>
            </div>

            <el-form ref="awardForm" :model="awardData" :rules="rules" label-width="120px">
                <div class="form-section">
                    <el-form-item label="实际奖金" prop="prize_amount">
                        <el-input-number
                            v-model="awardData.prize_amount"
                            :precision="2"
                            :step="0.01"
                            :min="0"
                            :max="999999.99"
                            style="width: 200px;"
                            @change="calculateActualAmount">
                        </el-input-number>
                        <span style="margin-left: 10px; color: #909399;">可修改实际奖金金额</span>
                    </el-form-item>

                    <el-form-item label="扣税金额" prop="tax_amount">
                        <el-input-number
                            v-model="awardData.tax_amount"
                            :precision="2"
                            :step="0.01"
                            :min="0"
                            :max="999999.99"
                            style="width: 200px;"
                            @change="calculateActualAmount">
                        </el-input-number>
                        <span style="margin-left: 10px; color: #909399;">扣税金额，可以为0</span>
                    </el-form-item>

                    <el-form-item label="实际到账">
                        <span :class="['actual-amount', actualAmount < 0 ? 'negative' : '']">
                            ¥{{ formatAmount(actualAmount) }}
                        </span>
                        <span style="margin-left: 10px; color: #909399;">实际到账 = 实际奖金 - 扣税金额</span>
                    </el-form-item>

                    <el-form-item label="派奖后余额">
                        <span class="amount-highlight">¥{{ formatAmount(user.money + actualAmount) }}</span>
                    </el-form-item>
                </div>

                <div class="form-buttons">
                    <el-button 
                        type="primary" 
                        size="medium"
                        :loading="loading"
                        @click="submitAward"
                        :disabled="actualAmount <= 0">
                        <i class="el-icon-money"></i> 确认派奖
                    </el-button>
                    <el-button 
                        size="medium" 
                        @click="closeDialog">
                        <i class="el-icon-close"></i> 取消
                    </el-button>
                </div>
            </el-form>
        </el-card>
    </div>
</div>

<script type="text/javascript">
    // 传递数据到全局变量供JS使用
    window.awardPageData = {
        order: {$order|json_encode},
        user: {$user|json_encode}
    };
</script>

</body>
</html>