<?php

namespace app\admin\controller\lottery\order;

use app\common\controller\Backend;

/**
 * 排列五投注管理
 *
 * @icon fa fa-circle-o
 */
class Plw extends Backend
{

    /**
     * Plw模型对象
     * @var \app\admin\model\lottery\order\Plw
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\lottery\order\Plw;
        $this->view->assign("betTypeList", $this->model->getBetTypeList());
    }



    /**
     * 测试排列五订单数据丰富功能
     */
    public function testEnrichData()
    {
        $orderId = $this->request->get('order_id', 72);

        try {
            $orderItems = \addons\lottery\model\OrderPlw::getSubOrder($orderId);

            if (empty($orderItems)) {
                $this->error('未找到订单信息');
            }

            $firstItem = $orderItems[0];

            $result = [
                'order_info' => [
                    'order_id' => $firstItem->order_id,
                    'user_id' => $firstItem->user_id,
                    'issue_id' => $firstItem->issue_id,
                    'items_count' => count($orderItems)
                ],
                'order_items' => [],
                'has_result_data' => false
            ];

            // 处理订单项数据
            foreach ($orderItems as $item) {
                $itemData = [
                    'id' => $item->id,
                    'bet_type' => $item->bet_type,
                    'bet_amount' => $item->bet_amount,
                    'bet_count' => $item->bet_count,
                    'bet_multiple' => $item->bet_multiple,
                    'bet_data' => $item->bet_data
                ];
                $result['order_items'][] = $itemData;
            }

            // 检查是否有开奖结果数据
            if (isset($firstItem->result_data) && !empty($firstItem->result_data)) {
                $result['has_result_data'] = true;
                $result['result_data'] = $firstItem->result_data;
            }

            $this->success('获取成功', $result);

        } catch (\Exception $e) {
            $this->error('测试失败: ' . $e->getMessage());
        }
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


}
