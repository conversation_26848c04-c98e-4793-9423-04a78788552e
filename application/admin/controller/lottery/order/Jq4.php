<?php

namespace app\admin\controller\lottery\order;

use app\common\controller\Backend;

/**
 * 四场进球彩订单管理
 *
 * @icon fa fa-circle-o
 */
class Jq4 extends Backend
{

    /**
     * Jq4模型对象
     * @var \app\admin\model\lottery\order\Jq4
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\lottery\order\Jq4;

    }



    /**
     * 测试四场进球订单数据丰富功能
     */
    public function testEnrichData()
    {
        $orderId = $this->request->get('order_id', 76);

        try {
            $orderInfo = \addons\lottery\model\OrderJq4::getSubOrder($orderId);

            if (!$orderInfo) {
                $this->error('未找到订单信息');
            }

            $result = [
                'order_info' => [
                    'id' => $orderInfo->id,
                    'order_id' => $orderInfo->order_id,
                    'user_id' => $orderInfo->user_id,
                    'amount' => $orderInfo->amount,
                    'issue_id' => $orderInfo->issue_id,
                    'bet_count' => $orderInfo->bet_count,
                    'bet_multiple' => $orderInfo->bet_multiple
                ],
                'bet_data' => $orderInfo->bet_data,
                'bet_data_count' => count($orderInfo->bet_data),
                'has_result_data' => false
            ];

            // 检查是否有期号比赛结果数据
            if (isset($orderInfo->result_data) && !empty($orderInfo->result_data)) {
                $result['has_result_data'] = true;
                $result['result_data'] = $orderInfo->result_data;
            }

            $this->success('获取成功', $result);

        } catch (\Exception $e) {
            $this->error('测试失败: ' . $e->getMessage());
        }
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除须需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


}
