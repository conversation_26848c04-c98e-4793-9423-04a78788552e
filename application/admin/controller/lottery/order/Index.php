<?php

namespace app\admin\controller\lottery\order;

use app\common\controller\Backend;
use app\admin\library\Auth;
use Exception;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use PhpOffice\PhpSpreadsheet\Reader\Xls;
use PhpOffice\PhpSpreadsheet\Reader\Csv;
use think\Db;
use think\db\exception\BindParamException;
use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\exception\DbException;
use think\exception\PDOException;
use think\exception\ValidateException;
use think\response\Json;
/**
 * 用户投注主管理
 *
 * @icon fa fa-circle-o
 */
class Index extends Backend
{

    /**
     * Index模型对象
     * @var \app\admin\model\lottery\order\Index
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\lottery\order\Index;
        $this->view->assign("statusList", $this->model->getStatusList());
        $this->view->assign("lotteryTypeList", $this->model->getLotteryTypeList());
        $this->view->assign("unionStatusList", $this->model->getUnionStatusList());
        $this->view->assign("unionList", $this->model->getUnionList());
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */

        /**
     * 查看
     *
     * @return string|Json
     * @throws \think\Exception
     * @throws DbException
     */
    public function index()
    {
        $adminStoreWhere = getAdminStoreWhere();
        
        // 获取当前管理员权限信息
        $currentAdmin = $this->auth->getUserinfo();
        $adminModel = new \app\admin\model\Admin();
        $adminInfo = $adminModel->get($currentAdmin['id']);
        
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if (false === $this->request->isAjax()) {
            return $this->view->fetch();
        }
        //如果发送的来源是 Selectpage，则转发到 Selectpage
        if ($this->request->request('keyField')) {
            return $this->selectpage();
        }
        [$where, $sort, $order, $offset, $limit] = $this->buildparams();

        // union字段现在只有0和1两个值，不需要特殊处理

        // 添加管理员权限过滤
        $adminWhere = [];
        
        // 1. 金额范围过滤
        if ($adminInfo && !$this->auth->isSuperAdmin()) {
            // 最小金额过滤
            if ($adminInfo->min_order_amount > 0) {
                $adminWhere[] = ['total_amount', '>=', $adminInfo->min_order_amount];
            }
            
            // 最大金额过滤
            if ($adminInfo->max_order_amount > 0) {
                $adminWhere[] = ['total_amount', '<=', $adminInfo->max_order_amount];
            }
            
            // 2. 彩种类型过滤
            if (!empty($adminInfo->allowed_lottery_types)) {
                $allowedTypes = explode(',', $adminInfo->allowed_lottery_types);
                $adminWhere[] = ['lottery_type', 'IN', $allowedTypes];
            }
        }
        
        $query = $this->model
            ->with(['user' => function($query) {
                $query->field('id,username,nickname,mobile,remark');
            }])
            ->with(['unionPlan' => function($query) {
                $query->field('id,order_id,total_shares,purchased_shares,creator_shares,unit_price,status');
            }])
            ->where($where)
            ->where($adminStoreWhere);

        // 应用管理员权限过滤
        if (!empty($adminWhere)) {
            foreach ($adminWhere as $condition) {
                $query->where($condition[0], $condition[1], $condition[2]);
            }
        }

        $list = $query->order($sort, $order)->paginate($limit);
        $result = ['total' => $list->total(), 'rows' => $list->items()];
        return json($result);
    }

    public function detail(){
        if(!$this->request->isPost()){
            return $this->view->fetch();
        }

        $id = $this->request->param('id');

        // 验证必要参数
        if (!$id) {
            $this->error('订单ID不能为空');
        }
        
        // 获取当前管理员权限信息
        $currentAdmin = $this->auth->getUserinfo();
        $adminModel = new \app\admin\model\Admin();
        $adminInfo = $adminModel->get($currentAdmin['id']);
        
        // 构建查询条件
        $where = ['id' => $id];
        $adminStoreWhere = getAdminStoreWhere();

        $order = $this->model->where($where)->find();

        if (!$order) {
            $this->error('订单不存在');
        }
        
        // 检查管理员权限（非超管需要检查）
        if ($adminInfo && !$this->auth->isSuperAdmin()) {
            // 检查金额权限
            if ($adminInfo->min_order_amount > 0 && $order['total_amount'] < $adminInfo->min_order_amount) {
                $this->error('无权查看此订单：金额低于权限范围');
            }
            
            if ($adminInfo->max_order_amount > 0 && $order['total_amount'] > $adminInfo->max_order_amount) {
                $this->error('无权查看此订单：金额超出权限范围');
            }
            
            // 检查彩种权限
            if (!empty($adminInfo->allowed_lottery_types)) {
                $allowedTypes = explode(',', $adminInfo->allowed_lottery_types);
                if (!in_array($order['lottery_type'], $allowedTypes)) {
                    $this->error('无权查看此订单：彩种不在权限范围内');
                }
            }
        }

        // 获取关联的子订单信息
        if (!empty($order['lottery_type'])) {
            // 动态构造子订单模型类名
            $subOrderClass = "\\app\\admin\\model\\lottery\\order\\" . ucfirst($order['lottery_type']);

            if (class_exists($subOrderClass)) {
                $subOrderModel = new $subOrderClass();

                // 对于大乐透、七星彩、排列三和排列5，获取所有子订单；对于其他类型，获取单个子订单
                if (in_array($order['lottery_type'], ['dlt', 'qxc', 'pls' , 'plw', 'rx9', 'bqc6', 'jq4', 'sfc'])) {
                    $subOrder = $subOrderModel->where('order_id', $id)->select();
                } else {
                    $subOrder = $subOrderModel->where('order_id', $id)->find();
                }

                $order['sub_order'] = $subOrder;
            }
        }

        // 如果是合买订单，获取合买详细信息
        if ($order['union'] == 1) {
            $order['union_info'] = $this->getUnionOrderDetail($id);
        }

        $this->success('','',$order);

    }

    /**
     * 获取合买订单详细信息
     *
     * @param int $orderId 订单ID
     * @return array
     */
    private function getUnionOrderDetail($orderId)
    {
        // 获取合买方案信息
        $unionPlan = Db::name('lottery_union_plan')
            ->where('order_id', $orderId)
            ->find();

        if (!$unionPlan) {
            return null;
        }

        // 获取参与记录，包含用户信息
        $participations = Db::name('lottery_union_participation')
            ->alias('p')
            ->join('user u', 'p.user_id = u.id', 'LEFT')
            ->where('p.union_plan_id', $unionPlan['id'])
            ->field('p.*, u.username, u.nickname, u.mobile')
            ->order('p.is_creator desc, p.createtime asc')
            ->select();

        // 计算统计信息
        $stats = [
            'total_participants' => count($participations),
            'paid_participants' => 0,
            'unpaid_participants' => 0,
            'total_paid_amount' => 0,
            'progress_percent' => 0
        ];

        foreach ($participations as &$participation) {
            if ($participation['status'] == 1) {
                $stats['paid_participants']++;
                $stats['total_paid_amount'] += $participation['total_amount'];
            } else {
                $stats['unpaid_participants']++;
            }

            // 格式化时间
            $participation['createtime_text'] = date('Y-m-d H:i:s', $participation['createtime']);

            // 格式化用户显示名
            $participation['user_display'] = $participation['nickname'] ?: $participation['username'];
            if ($participation['mobile']) {
                $participation['user_display'] .= ' (' . substr($participation['mobile'], 0, 3) . '****' . substr($participation['mobile'], -4) . ')';
            }
        }

        // 计算认购进度
        if ($unionPlan['total_shares'] > 0) {
            $stats['progress_percent'] = round(($unionPlan['purchased_shares'] / $unionPlan['total_shares']) * 100, 2);
        }

        return [
            'plan' => $unionPlan,
            'participations' => $participations,
            'stats' => $stats
        ];
    }

    /**
     * 退单操作（支持单个和批量）
     * 按照 FastAdmin 标准实现
     */
    public function refund($ids = null)
    {
        if (!$this->request->isPost()) {
            $this->error('请求方式错误');
        }

        // 获取 ids 参数，优先使用方法参数，其次使用 POST 参数
        $ids = $ids ?: $this->request->post("ids");
        if (empty($ids)) {
            $this->error('订单ID不能为空');
        }

        // 确保 ids 是数组格式
        if (!is_array($ids)) {
            $ids = explode(',', $ids);
        }

        // 获取退单原因
        $refund_reason = $this->request->post('refund_reason', '');
        if (empty($refund_reason)) {
            $refund_reason = count($ids) > 1 ? '批量退单' : '退单';
        }

        // 获取当前管理员权限信息
        $currentAdmin = $this->auth->getUserinfo();
        $adminModel = new \app\admin\model\Admin();
        $adminInfo = $adminModel->get($currentAdmin['id']);

        $successCount = 0;
        $failedOrders = [];

        // 批量处理每个订单
        foreach ($ids as $id) {
            try {
                // 验证订单权限（专门用于退单）
                $order = $this->validateOrderForRefund($id, $adminInfo);

                // 执行退单操作
                $this->model->refundOrder($id, $refund_reason);
                $successCount++;

            } catch (\Exception $e) {
                $failedOrders[] = [
                    'id' => $id,
                    'error' => $e->getMessage()
                ];
            }
        }

        // 返回结果
        if ($successCount > 0) {
            if (empty($failedOrders)) {
                $this->success("成功退单 {$successCount} 个订单");
            } else {
                $this->success("成功退单 {$successCount} 个订单", '', [
                    'success_count' => $successCount,
                    'failed_orders' => $failedOrders
                ]);
            }
        } else {
            $this->error('退单失败', '', [
                'failed_orders' => $failedOrders
            ]);
        }
    }



    /**
     * 出票操作
     * 更新订单的票据图片和状态
     */
    public function ticket()
    {
        if (!$this->request->isPost()) {
            $this->error('请求方式错误');
        }

        $params = $this->request->post();

        // 验证必要参数
        if (!isset($params['id']) || empty($params['id'])) {
            $this->error('订单ID不能为空');
        }
        if (!isset($params['ticket_images']) || empty($params['ticket_images'])) {
            $this->error('票据图片不能为空');
        }

        $id = $params['id'];
        $ticket_images = $params['ticket_images'];
        
        // 获取当前管理员权限信息
        $currentAdmin = $this->auth->getUserinfo();
        $adminModel = new \app\admin\model\Admin();
        $adminInfo = $adminModel->get($currentAdmin['id']);
        
        // 构建查询条件
        $where['id'] = $id;
        $adminStoreWhere = getAdminStoreWhere();

        $order = $this->model->where($where)->where($adminStoreWhere)->find();


        if (!$order) {
            $this->error('订单不存在');
        }
        
        // 检查管理员权限（非超管需要检查）
        if ($adminInfo && !$this->auth->isSuperAdmin()) {
            // 检查金额权限
            if ($adminInfo->min_order_amount > 0 && $order['total_amount'] < $adminInfo->min_order_amount) {
                $this->error('无权操作此订单：金额低于权限范围');
            }
            
            if ($adminInfo->max_order_amount > 0 && $order['total_amount'] > $adminInfo->max_order_amount) {
                $this->error('无权操作此订单：金额超出权限范围');
            }
            
            // 检查彩种权限
            if (!empty($adminInfo->allowed_lottery_types)) {
                $allowedTypes = explode(',', $adminInfo->allowed_lottery_types);
                if (!in_array($order['lottery_type'], $allowedTypes)) {
                    $this->error('无权操作此订单：彩种不在权限范围内');
                }
            }
        }

        try {
            $this->model->ticket($id, $ticket_images);
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        $this->success("出票成功");
    }

    /**
     * 更新投注赔率
     */
    public function updateOdds()
    {
        if (!$this->request->isPost()) {
            $this->error('请求方式错误');
        }

        $params = $this->request->post();

        // 验证必要参数
        if (!isset($params['id']) || empty($params['id'])) {
            $this->error('订单ID不能为空');
        }
        if (!isset($params['bet_data']) || empty($params['bet_data'])) {
            $this->error('投注数据不能为空');
        }

        $id = $params['id'];
        $bet_data = $params['bet_data'];
        
        // 获取当前管理员权限信息
        $currentAdmin = $this->auth->getUserinfo();
        $adminModel = new \app\admin\model\Admin();
        $adminInfo = $adminModel->get($currentAdmin['id']);
        
        // 构建查询条件
        $where = ['id' => $id];
        $adminStoreWhere = getAdminStoreWhere();
        
        $order = $this->model->where($where)->where($adminStoreWhere)->find();

        if (!$order) {
            $this->error('订单不存在');
        }
        
        // 检查管理员权限（非超管需要检查）
        if ($adminInfo && !$this->auth->isSuperAdmin()) {
            // 检查金额权限
            if ($adminInfo->min_order_amount > 0 && $order['total_amount'] < $adminInfo->min_order_amount) {
                $this->error('无权操作此订单：金额低于权限范围');
            }
            
            if ($adminInfo->max_order_amount > 0 && $order['total_amount'] > $adminInfo->max_order_amount) {
                $this->error('无权操作此订单：金额超出权限范围');
            }
            
            // 检查彩种权限
            if (!empty($adminInfo->allowed_lottery_types)) {
                $allowedTypes = explode(',', $adminInfo->allowed_lottery_types);
                if (!in_array($order['lottery_type'], $allowedTypes)) {
                    $this->error('无权操作此订单：彩种不在权限范围内');
                }
            }
        }

        try {
            $this->model->updateOdds($id, $bet_data);
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }

        $this->success("赔率修改成功");
    }

    /**
     * 派奖功能统一入口
     * GET请求：显示派奖页面（单个订单）
     * POST请求：处理派奖操作（支持批量）
     */
    public function award()
    {
        // 根据请求类型处理不同逻辑
        if (!$this->request->isPost()) {
            // GET请求：显示单个订单派奖页面
            return $this->showAwardPage();
        } else {
            // POST请求：处理批量派奖操作
            return $this->processBatchAward();
        }
    }

    /**
     * 显示派奖页面（单个订单）
     */
    private function showAwardPage()
    {
        // 获取订单ID参数
        $id = $this->request->param('id');
        if (!$id) {
            $this->error('订单ID不能为空');
        }

        // 获取当前管理员权限信息
        $currentAdmin = $this->auth->getUserinfo();
        $adminModel = new \app\admin\model\Admin();
        $adminInfo = $adminModel->get($currentAdmin['id']);

        // 构建查询条件
        $where = ['id' => $id];
        $adminStoreWhere = getAdminStoreWhere();

        $order = $this->model->where($where)->where($adminStoreWhere)->find();

        if (!$order) {
            $this->error('订单不存在');
        }

        // 验证订单是否可以派奖
        $this->validateOrderForAward($order, $adminInfo);

        // 获取用户信息
        $user = \app\common\model\User::get($order['user_id']);

        $this->view->assign('order', $order);
        $this->view->assign('user', $user);
        return $this->view->fetch();
    }

    /**
     * 处理批量派奖操作
     */
    private function processBatchAward()
    {
        $params = $this->request->post();

        // 获取订单IDs参数（支持批量）
        $ids = $params['ids'] ?? [];
        if (empty($ids)) {
            $this->error('订单ID不能为空');
        }

        // 确保ids是数组格式
        if (!is_array($ids)) {
            $ids = explode(',', $ids);
        }

        // 批量派奖使用订单中已记录的奖金信息，不需要额外参数

        // 获取当前管理员权限信息
        $currentAdmin = $this->auth->getUserinfo();
        $adminModel = new \app\admin\model\Admin();
        $adminInfo = $adminModel->get($currentAdmin['id']);

        $successCount = 0;
        $failedOrders = [];

        // 批量处理每个订单
        foreach ($ids as $id) {
            try {
                // 验证订单
                $order = $this->validateOrderById($id, $adminInfo);

                // 使用订单中已记录的奖金信息执行派奖
                // 默认扣税金额为0，使用订单中的prize_amount作为奖金
                $prize_amount = floatval($order['prize_amount']);
                $tax_amount = 0; // 批量派奖默认不扣税

                $this->model->awardPrize($id, $prize_amount, $tax_amount);
                $successCount++;

            } catch (\Exception $e) {
                $failedOrders[] = [
                    'id' => $id,
                    'error' => $e->getMessage()
                ];
            }
        }

        // 返回批量处理结果
        if ($successCount > 0 && empty($failedOrders)) {
            $this->success("批量派奖成功，共处理 {$successCount} 个订单");
        } elseif ($successCount > 0 && !empty($failedOrders)) {
            $this->success("部分派奖成功，成功 {$successCount} 个，失败 " . count($failedOrders) . " 个", [
                'success_count' => $successCount,
                'failed_orders' => $failedOrders
            ]);
        } else {
            $this->error("批量派奖失败", [
                'failed_orders' => $failedOrders
            ]);
        }
    }

    /**
     * 验证订单是否可以派奖
     */
    private function validateOrderForAward($order, $adminInfo)
    {
        // 检查订单状态，只能对已中奖待派奖(status=3)的订单进行派奖
        if ($order['status'] != 3) {
            $this->error('只能对已中奖待派奖的订单进行派奖');
        }

        // 检查是否已经派奖（如果awarded_amount > 0说明已经派过奖）
        if ($order['awarded_amount'] > 0) {
            $this->error('该订单已经派奖，不能重复派奖');
        }

        // 检查管理员权限（非超管需要检查）
        if ($adminInfo && !$this->auth->isSuperAdmin()) {
            // 检查金额权限
            if ($adminInfo->min_order_amount > 0 && $order['total_amount'] < $adminInfo->min_order_amount) {
                $this->error('无权操作此订单：金额低于权限范围');
            }

            if ($adminInfo->max_order_amount > 0 && $order['total_amount'] > $adminInfo->max_order_amount) {
                $this->error('无权操作此订单：金额超出权限范围');
            }

            // 检查彩种权限
            if (!empty($adminInfo->allowed_lottery_types)) {
                $allowedTypes = explode(',', $adminInfo->allowed_lottery_types);
                if (!in_array($order['lottery_type'], $allowedTypes)) {
                    $this->error('无权操作此订单：彩种不在权限范围内');
                }
            }
        }
    }

    /**
     * 根据ID验证订单并返回订单信息（用于派奖）
     */
    private function validateOrderById($id, $adminInfo)
    {
        // 构建查询条件
        $where = ['id' => $id];
        $adminStoreWhere = getAdminStoreWhere();

        $order = $this->model->where($where)->where($adminStoreWhere)->find();

        if (!$order) {
            throw new \Exception("订单ID {$id} 不存在");
        }

        // 验证订单是否可以派奖
        $this->validateOrderForAward($order, $adminInfo);

        return $order;
    }

    /**
     * 验证订单是否可以退单
     */
    private function validateOrderForRefund($id, $adminInfo)
    {
        // 构建查询条件
        $where = ['id' => $id];
        $adminStoreWhere = getAdminStoreWhere();

        $order = $this->model->where($where)->where($adminStoreWhere)->find();

        if (!$order) {
            throw new \Exception("订单ID {$id} 不存在");
        }

        // 检查订单状态，只能对待出票(1)和待开奖(2)状态的订单进行退单
        if (!in_array($order['status'], [1, 2])) {
            $statusTexts = [
                0 => '未付款',
                1 => '待出票',
                2 => '待开奖',
                3 => '已中奖',
                4 => '已派奖',
                5 => '未中奖',
                8 => '已退单',
                9 => '已取消'
            ];
            $currentStatusText = $statusTexts[$order['status']] ?? '未知状态';
            throw new \Exception("当前订单状态为【{$currentStatusText}】，只有【待出票】和【待开奖】状态的订单可以退单");
        }

        // 检查管理员权限（非超管需要检查）
        if ($adminInfo && !$this->auth->isSuperAdmin()) {
            // 检查金额权限
            if ($adminInfo->min_order_amount > 0 && $order['total_amount'] < $adminInfo->min_order_amount) {
                throw new \Exception('无权操作此订单：金额低于权限范围');
            }

            if ($adminInfo->max_order_amount > 0 && $order['total_amount'] > $adminInfo->max_order_amount) {
                throw new \Exception('无权操作此订单：金额超出权限范围');
            }

            // 检查彩种权限
            if (!empty($adminInfo->allowed_lottery_types)) {
                $allowedTypes = explode(',', $adminInfo->allowed_lottery_types);
                if (!in_array($order['lottery_type'], $allowedTypes)) {
                    throw new \Exception('无权操作此订单：彩种不在权限范围内');
                }
            }
        }

        return $order;
    }

}
