<?php

namespace app\admin\controller\lottery\statistics;

use app\common\controller\Backend;
use think\Db;

/**
 * 彩票统计管理
 *
 * @icon fa fa-bar-chart
 */
class Index extends Backend
{

    /**
     * Statistics模型对象
     * @var \app\admin\model\lottery\statistics\Index
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\lottery\statistics\Index;
    }

    /**
     * 统计首页
     */
    public function index()
    {
        if ($this->request->isAjax()) {
            $dateRange = $this->request->post('date_range', '');
            $storeId = $this->request->post('store_id', '');
            
            // 获取当前管理员信息
            $admin = $this->auth->getUserinfo();
            $adminStoreId = isset($admin['store_id']) ? $admin['store_id'] : 0;
            
            // 权限控制：如果管理员有store_id，则只能查看自己店铺的数据
            if ($adminStoreId > 0) {
                $storeId = $adminStoreId;
            }
            
            // 解析日期范围
            $startTime = '';
            $endTime = '';
            if ($dateRange) {
                $dates = explode(' - ', $dateRange);
                if (count($dates) == 2) {
                    $startTime = strtotime($dates[0] . ' 00:00:00');
                    $endTime = strtotime($dates[1] . ' 23:59:59');
                }
            }
            
            $data = [
                'overview' => $this->model->getOverviewStats($storeId, $startTime, $endTime),
                'store_stats' => $this->model->getStoreStats($storeId, $startTime, $endTime),
                'lottery_type_stats' => $this->model->getLotteryTypeStats($storeId, $startTime, $endTime),
                'daily_stats' => $this->model->getDailyStats($storeId, $startTime, $endTime),
                'user_stats' => $this->model->getUserStats($storeId, $startTime, $endTime),
                'finance_stats' => $this->model->getFinanceStats($storeId, $startTime, $endTime),
                'member_fund_overview' => $this->model->getMemberFundOverview($storeId, $startTime, $endTime)
            ];
            
            $this->success('获取成功', '', $data);
        }
        
        // 获取当前管理员信息
        $admin = $this->auth->getUserinfo();
        $adminStoreId = isset($admin['store_id']) ? $admin['store_id'] : 0;
        
        // 获取店铺列表：如果管理员有store_id则只显示自己的店铺，否则显示所有店铺
        $storeModel = new \app\admin\model\lottery\store\Index();
        if ($adminStoreId > 0) {
            $stores = $storeModel->where('id', $adminStoreId)
                ->where('status', 1)
                ->field('id,name')
                ->select();
        } else {
            $stores = $storeModel->where('status', 1)
                ->field('id,name')
                ->select();
        }

        // 转换为数组格式，确保前端能正确接收
        $storesArray = [];
        if ($stores) {
            foreach ($stores as $store) {
                $storesArray[] = [
                    'id' => $store['id'],
                    'name' => $store['name']
                ];
            }
        }

        // 使用 assignconfig 传递配置数据给前端
        $this->assignconfig('stores', $storesArray);
        $this->assignconfig('adminStoreId', $adminStoreId);


        return $this->view->fetch();
    }

    /**
     * 会员资金统计
     */
    public function memberFund()
    {
        if ($this->request->isAjax()) {
            // 获取筛选参数
            $page = $this->request->post('page', 1);
            $limit = $this->request->post('limit', 20);
            $search = $this->request->post('search', '');
            $sortField = $this->request->post('sort_field', 'register_time');
            $sortOrder = $this->request->post('sort_order', 'desc');

            // 获取当前管理员信息
            $admin = $this->auth->getUserinfo();
            $adminStoreId = isset($admin['store_id']) ? $admin['store_id'] : 0;

            // 获取会员资金统计数据
            $result = $this->model->getMemberFundStats($adminStoreId, $search, $page, $limit, $sortField, $sortOrder);

            $this->success('获取成功', '', $result);
        }

        $this->error('请求方式错误');
    }


}