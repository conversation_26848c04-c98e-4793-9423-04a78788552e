<?php

namespace app\admin\controller\lottery;

use app\common\controller\Backend;
use addons\lottery\model\RechargeRecord;
use addons\lottery\model\WithdrawalRecord;
use addons\lottery\model\Order;

/**
 * 彩票通知控制器
 */
class Notification extends Backend
{
    /**
     * 无需鉴权的方法,但需要登录
     * @var array
     */
    protected $noNeedRight = ['getUnreadCount', 'getLatestUnread'];

    /**
     * 获取管理员权限过滤条件（用于订单筛选）
     */
    private function getAdminPermissionWhere()
    {
        $adminWhere = [];
        
        // 获取当前管理员权限信息
        if (!$this->auth->isSuperAdmin()) {
            $currentAdmin = $this->auth->getUserinfo();
            $adminModel = new \app\admin\model\Admin();
            $adminInfo = $adminModel->get($currentAdmin['id']);
            
            if ($adminInfo) {
                // 最小金额过滤
                if ($adminInfo->min_order_amount > 0) {
                    $adminWhere[] = ['total_amount', '>=', $adminInfo->min_order_amount];
                }
                
                // 最大金额过滤
                if ($adminInfo->max_order_amount > 0) {
                    $adminWhere[] = ['total_amount', '<=', $adminInfo->max_order_amount];
                }
                
                // 彩种类型过滤
                if (!empty($adminInfo->allowed_lottery_types)) {
                    $allowedTypes = explode(',', $adminInfo->allowed_lottery_types);
                    $adminWhere[] = ['lottery_type', 'IN', $allowedTypes];
                }
            }
        }
        
        return $adminWhere;
    }

    /**
     * 获取未读通知数量
     */
    public function getUnreadCount()
    {
        // 获取管理员的店铺权限
        $adminStoreWhere = getAdminStoreWhere();
        
        // 获取管理员权限过滤条件（仅用于订单）
        $adminPermissionWhere = $this->getAdminPermissionWhere();
        
        // 获取待审核的充值记录数量 (status = 3)
//        $rechargeWhere = array_merge($adminStoreWhere, ['status' => 3]);
        $rechargeUnreadCount = RechargeRecord::where($adminStoreWhere)->where(['status'=>3])->count();
        
        // 获取待处理的提现记录数量 (status = 0)
//        $withdrawalWhere = array_merge($adminStoreWhere, ['status' => 0]);
        $withdrawalUnreadCount = WithdrawalRecord::where($adminStoreWhere)->where(['status' => 0])->count();
        
        // 获取待出票的订单数量 (status = 1) - 应用管理员权限过滤
        $orderQuery = Order::where($adminStoreWhere)->where(['status' => 1]);
        
        // 应用管理员权限过滤条件
        if (!empty($adminPermissionWhere)) {
            foreach ($adminPermissionWhere as $condition) {
                $orderQuery->where($condition[0], $condition[1], $condition[2]);
            }
        }
        
        $orderUnreadCount = $orderQuery->count();
        
        $data = [
            'recharge_unread' => $rechargeUnreadCount,
            'withdrawal_unread' => $withdrawalUnreadCount,
            'order_unread' => $orderUnreadCount,
            'total_unread' => $rechargeUnreadCount + $withdrawalUnreadCount + $orderUnreadCount
        ];
        
        $this->success('获取成功', '', $data);
    }
    
    /**
     * 获取最新的未读记录
     */
    public function getLatestUnread()
    {
        // 获取管理员的店铺权限
        $adminStoreWhere = getAdminStoreWhere();
        
        // 获取管理员权限过滤条件（仅用于订单）
        $adminPermissionWhere = $this->getAdminPermissionWhere();
        
        // 获取最新的待审核充值记录 (status = 3)
//        $rechargeWhere = array_merge($adminStoreWhere, ['status' => 3]);
        $latestRecharge = RechargeRecord::where($adminStoreWhere)->where(['status' => 3])
            ->order('createtime', 'desc')
            ->limit(5)
            ->select();
        
        // 获取最新的待处理提现记录 (status = 0)
//        $withdrawalWhere = array_merge($adminStoreWhere, ['status' => 0]);
        $latestWithdrawal = WithdrawalRecord::where($adminStoreWhere)->where(['status' => 0])
            ->order('createtime', 'desc')
            ->limit(5)
            ->select();
        
        // 获取最新的待出票订单 (status = 1) - 应用管理员权限过滤
        $orderQuery = Order::where($adminStoreWhere)->where(['status' => 1]);
        
        // 应用管理员权限过滤条件
        if (!empty($adminPermissionWhere)) {
            foreach ($adminPermissionWhere as $condition) {
                $orderQuery->where($condition[0], $condition[1], $condition[2]);
            }
        }
        
        $latestOrders = $orderQuery->order('createtime', 'desc')->limit(5)->select();
        
        $data = [
            'recharge_records' => $latestRecharge,
            'withdrawal_records' => $latestWithdrawal,
            'order_records' => $latestOrders
        ];
        
        $this->success('获取成功', '', $data);
    }
    
    /**
     * 获取历史已处理记录
     */
    public function getProcessedRecords()
    {
        // 获取管理员的店铺权限
        $adminStoreWhere = getAdminStoreWhere();
        
        // 获取最近已处理的充值记录 (status = 4 审核通过 或 status = 5 审核驳回)
        $rechargeWhere = array_merge($adminStoreWhere, []);
        $processedRecharge = RechargeRecord::where($rechargeWhere)
            ->where('status', 'IN', [4, 5])
            ->order('updatetime', 'desc')
            ->limit(10)
            ->select();
        
        // 获取最近已处理的提现记录 (status = 1 已批准 或 status = 2 已拒绝 或 status = 3 已完成)
        $withdrawalWhere = array_merge($adminStoreWhere, []);
        $processedWithdrawal = WithdrawalRecord::where($withdrawalWhere)
            ->where('status', 'IN', [1, 2, 3])
            ->order('updatetime', 'desc')
            ->limit(10)
            ->select();
        
        $data = [
            'processed_recharge' => $processedRecharge,
            'processed_withdrawal' => $processedWithdrawal
        ];
        
        $this->success('获取成功', '', $data);
    }
}