<?php

namespace app\admin\controller\lottery\recharge;

use app\common\controller\Backend;
use think\response\Json;

/**
 * 充值记录
 *
 * @icon fa fa-circle-o
 */
class Record extends Backend
{

    /**
     * Record模型对象
     * @var \app\admin\model\lottery\recharge\Record
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\lottery\recharge\Record;
        $this->view->assign("statusList", $this->model->getStatusList());
        $this->view->assign("isReadList", $this->model->getIsReadList());
        $this->view->assign("paymentMethodList", $this->model->getPaymentMethodList());
    }

    /**
     * 查看
     *
     * @return string|Json
     * @throws \think\Exception
     */
    public function index()
    {
        $adminStoreWhere = getAdminStoreWhere();
        
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if (false === $this->request->isAjax()) {
            return $this->view->fetch();
        }
        //如果发送的来源是 Selectpage，则转发到 Selectpage
        if ($this->request->request('keyField')) {
            return $this->selectpage();
        }
        [$where, $sort, $order, $offset, $limit] = $this->buildparams();
        $list = $this->model
            ->with(['user' => function($query) {
                $query->field('id,username,nickname,mobile,remark');
            }])
            ->where($where)
            ->where($adminStoreWhere)
            ->order($sort, $order)
            ->paginate($limit);
        $result = ['total' => $list->total(), 'rows' => $list->items()];
        return json($result);
    }

    /**
     * 审核通过
     */
    public function approve()
    {
        $ids = $this->request->param('ids');
        if (!$ids) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }
        
        $id = is_array($ids) ? $ids[0] : $ids;
        $remark = $this->request->param('remark', '审核通过');
        
        try {
            // 获取充值记录并检查权限
            $adminStoreWhere = getAdminStoreWhere();
            $record = $this->model->where($adminStoreWhere)->where('id', $id)->find();
            if (!$record) {
                $this->error('充值记录不存在或无权限访问');
            }
            
            // 检查状态
            if ($record->status != 3) {
                $this->error('该记录不是待审核状态，无法审核');
            }
            
            // 更新记录状态为审核通过
            $record->status = 4; // 审核通过
            $record->actual_amount = $record->amount; // 使用原始金额作为实际到账金额
            $record->remark = $remark;
            $record->save();
            
            // 给用户增加余额
            \app\common\model\User::money($record->amount, $record->user_id, '充值审核通过');
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }
        
        $this->success('审核通过成功');
    }

    /**
     * 审核驳回
     */
    public function reject()
    {
        $ids = $this->request->param('ids');
        if (!$ids) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }
        
        $id = is_array($ids) ? $ids[0] : $ids;
        $remark = $this->request->param('remark', '审核驳回');
        
        try {
            // 获取充值记录并检查权限
            $adminStoreWhere = getAdminStoreWhere();
            $record = $this->model->where($adminStoreWhere)->where('id', $id)->find();
            if (!$record) {
                $this->error('充值记录不存在或无权限访问');
            }
            
            // 检查状态
            if ($record->status != 3) {
                $this->error('该记录不是待审核状态，无法审核');
            }
            
            // 更新记录状态为审核驳回
            $record->status = 5; // 审核驳回
            $record->remark = $remark;
            $record->save();
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }
        
        $this->success('审核驳回成功');
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


}
