<?php

namespace app\admin\controller\lottery;

use app\common\controller\Backend;
use addons\lottery\model\ChatConversation;
use addons\lottery\model\ChatMessage;
use addons\lottery\model\ChatConversationMember;
use addons\lottery\model\ChatFriend;
use addons\lottery\library\WebSocketService;
use app\common\model\User;
use addons\lottery\model\Store;
use think\Db;
use think\Env;

/**
 * 管理端聊天功能
 *
 * @icon fa fa-comments
 * @remark 管理端聊天功能，商户可以和用户聊天，超管可以和所有人聊天
 */
class Chat extends Backend
{
    protected $noNeedRight = ['*'];

    /**
     * 聊天首页
     */
    public function index()
    {
        // 获取当前管理员信息
        $admin = $this->auth->getUserinfo();
        $adminStoreId = isset($admin['store_id']) ? $admin['store_id'] : 0;
        $isSuperAdmin = $this->auth->isSuperAdmin();
        
        $this->assignconfig([
            'admin_id' => $admin['id'],
            'admin_store_id' => $adminStoreId,
            'is_super_admin' => $isSuperAdmin ? 1 : 0,
            'admin_info' => json_encode($admin),
            'ws' => Env::get('chat.websocket_address')
        ]);
        
        return $this->view->fetch();
    }

    /**
     * 获取会话列表
     */
    public function getConversations()
    {
        $admin = $this->auth->getUserinfo();
        $adminStoreId = isset($admin['store_id']) ? $admin['store_id'] : 0;
        $isSuperAdmin = $this->auth->isSuperAdmin();
        
        // 临时返回空数组，确保接口可用
        $conversations = [];
        
        // TODO: 实现实际的会话获取逻辑
        // $adminUserId = $this->getAdminUserId($admin['id']);
        // if (!$adminUserId) {
        //     $this->error('管理员未关联用户账号');
        // }
        
        // try {
        //     $conversationModel = new ChatConversation();
        //     $conversations = $conversationModel->getUserConversationList($adminUserId);
        //     
        //     // 确保返回数组格式
        //     if (!is_array($conversations)) {
        //         $conversations = [];
        //     }
        //     
        //     // 权限过滤：商户只能看到自己店铺用户的会话
        //     if (!$isSuperAdmin && $adminStoreId > 0) {
        //         $conversations = $this->filterConversationsByStore($conversations, $adminStoreId);
        //     }
        // } catch (\Exception $e) {
        //     $conversations = [];
        // }
        
        $this->success('获取会话列表成功', '', $conversations);
    }

    /**
     * 获取消息列表
     */
    public function getMessages()
    {
        $conversationId = $this->request->param('conversation_id/d');
        $page = $this->request->param('page/d', 1);
        $limit = $this->request->param('limit/d', 50);
        
        if (!$conversationId) {
            $this->error('会话ID不能为空');
        }
        
        // 验证权限
        if (!$this->validateConversationAccess($conversationId)) {
            $this->error('无权限访问该会话');
        }
        
        $admin = $this->auth->getUserinfo();
        $adminUserId = $this->getAdminUserId($admin['id']);
        
        $messageModel = new ChatMessage();
        $messages = $messageModel->getMessageList($conversationId, $adminUserId, $page, $limit);
        
        $this->success('获取消息列表成功', '', $messages);
    }

    /**
     * 发送消息
     */
    public function sendMessage()
    {
        $conversationId = $this->request->param('conversation_id/d');
        $content = $this->request->param('content');
        $contentType = $this->request->param('type/d', 1);
        $extraData = $this->request->param('extra_data');
        
        if (!$conversationId) {
            $this->error('会话ID不能为空');
        }
        
        if (!$content) {
            $this->error('消息内容不能为空');
        }
        
        // 验证权限
        if (!$this->validateConversationAccess($conversationId)) {
            $this->error('无权限访问该会话');
        }
        
        $admin = $this->auth->getUserinfo();
        $adminUserId = $this->getAdminUserId($admin['id']);
        
        $messageModel = new ChatMessage();
        $messageId = $messageModel->sendMessage(
            $conversationId, 
            $adminUserId, 
            $content, 
            $contentType,
            $extraData
        );
        
        if (!$messageId) {
            $this->error('发送消息失败');
        }
        
        // 获取发送的消息详情
        $message = $messageModel->getMessageDetail($messageId);
        
        // 通过WebSocket实时推送消息
        $this->pushMessageToWebSocket($conversationId, $message, $adminUserId);
        
        $this->success('发送消息成功', '', $message);
    }

    /**
     * 获取用户列表（用于发起聊天）
     */
    public function getUsers()
    {
        $keyword = $this->request->param('keyword', '');
        $page = $this->request->param('page/d', 1);
        $limit = $this->request->param('limit/d', 20);
        
        $admin = $this->auth->getUserinfo();
        $adminStoreId = isset($admin['store_id']) ? $admin['store_id'] : 0;
        $isSuperAdmin = $this->auth->isSuperAdmin();
        
        $userModel = new User();
        $where = [];
        
        // 搜索条件
        if ($keyword) {
            $where[] = ['nickname|username|mobile', 'like', '%' . $keyword . '%'];
        }
        
        // 权限控制：商户只能看到自己店铺的用户
        if (!$isSuperAdmin && $adminStoreId > 0) {
            $where[] = ['store_id', '=', $adminStoreId];
        }
        
        $users = $userModel->where($where)
            ->field('id,username,nickname,mobile,avatar,store_id,createtime')
            ->page($page, $limit)
            ->order('id desc')
            ->select();
        
        $total = $userModel->where($where)->count();
        
        foreach ($users as &$user) {
            $user['avatar'] = cdnurl($user['avatar'], true);
        }
        
        $this->success('获取用户列表成功', '', [
            'list' => $users,
            'total' => $total,
            'page' => $page,
            'limit' => $limit
        ]);
    }

    /**
     * 创建会话
     */
    public function createConversation()
    {
        $userId = $this->request->param('user_id/d');
        
        if (!$userId) {
            $this->error('用户ID不能为空');
        }
        
        $admin = $this->auth->getUserinfo();
        $adminStoreId = isset($admin['store_id']) ? $admin['store_id'] : 0;
        $isSuperAdmin = $this->auth->isSuperAdmin();
        
        // 权限检查：商户只能和自己店铺的用户聊天
        if (!$isSuperAdmin && $adminStoreId > 0) {
            $user = User::get($userId);
            if (!$user || $user['store_id'] != $adminStoreId) {
                $this->error('无权限与该用户聊天');
            }
        }
        
        $adminUserId = $this->getAdminUserId($admin['id']);
        if (!$adminUserId) {
            $this->error('管理员未关联用户账号');
        }
        
        $conversationModel = new ChatConversation();
        
        // 检查是否已存在会话
        $existConversation = $conversationModel->getSingleChatConversation($adminUserId, $userId);
        if ($existConversation) {
            $this->success('会话已存在', '', $existConversation);
        }
        
        // 创建新会话
        $conversationId = $conversationModel->createSingleChat($adminUserId, $userId);
        if (!$conversationId) {
            $this->error('创建会话失败');
        }
        
        // 获取创建的会话详情
        $conversation = $conversationModel->getConversationDetail($conversationId, $adminUserId);
        
        $this->success('创建会话成功', '', $conversation);
    }

    /**
     * 获取店铺列表（超管专用）
     */
    public function getStores()
    {
        // 只有超管才能获取店铺列表
        if (!$this->auth->isSuperAdmin()) {
            $this->error('权限不足');
        }
        
        $storeModel = new Store();
        $stores = $storeModel->field('id,name,user_id,status')
            ->where('status', 'normal')
            ->select();
        
        $this->success('获取店铺列表成功', '', $stores);
    }

    // =============== 私有方法 ===============

    /**
     * 获取管理员对应的用户ID
     */
    private function getAdminUserId($adminId)
    {
        // 管理员使用负数ID来区分普通用户，避免ID冲突
        // 例如：管理员ID=1 对应聊天用户ID=-1
        return -$adminId;
    }

    /**
     * 验证会话访问权限
     */
    private function validateConversationAccess($conversationId)
    {
        $admin = $this->auth->getUserinfo();
        $adminUserId = $this->getAdminUserId($admin['id']);
        $adminStoreId = isset($admin['store_id']) ? $admin['store_id'] : 0;
        $isSuperAdmin = $this->auth->isSuperAdmin();
        
        if (!$adminUserId) {
            return false;
        }
        
        $memberModel = new ChatConversationMember();
        
        // 检查是否是会话成员
        if (!$memberModel->isMember($conversationId, $adminUserId)) {
            return false;
        }
        
        // 商户权限检查：只能访问涉及自己店铺用户的会话
        if (!$isSuperAdmin && $adminStoreId > 0) {
            $members = $memberModel->getConversationMembers($conversationId);
            $hasStoreUser = false;
            
            foreach ($members as $memberId) {
                if ($memberId == $adminUserId) {
                    continue; // 跳过管理员自己
                }
                
                $user = User::get($memberId);
                if ($user && $user['store_id'] == $adminStoreId) {
                    $hasStoreUser = true;
                    break;
                }
            }
            
            if (!$hasStoreUser) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * 根据店铺过滤会话列表
     */
    private function filterConversationsByStore($conversations, $storeId)
    {
        $filtered = [];
        
        foreach ($conversations as $conversation) {
            $conversationId = $conversation['id'];
            $memberModel = new ChatConversationMember();
            $members = $memberModel->getConversationMembers($conversationId);
            
            $hasStoreUser = false;
            foreach ($members as $memberId) {
                $user = User::get($memberId);
                if ($user && $user['store_id'] == $storeId) {
                    $hasStoreUser = true;
                    break;
                }
            }
            
            if ($hasStoreUser) {
                $filtered[] = $conversation;
            }
        }
        
        return $filtered;
    }

    /**
     * 推送消息到WebSocket
     */
    private function pushMessageToWebSocket($conversationId, $message, $adminUserId)
    {
        $memberModel = new ChatConversationMember();
        $members = $memberModel->getConversationMembers($conversationId);
        
        if (empty($members)) {
            return;
        }
        
        // 构建WebSocket消息数据
        $wsData = WebSocketService::buildChatMessage($message, 'new_message');
        
        // 向会话成员推送消息（排除发送者）
        WebSocketService::sendToConversation($members, $wsData, $adminUserId);
    }
}