<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use app\admin\model\lottery\order\Index as OrderModel;
use app\admin\model\lottery\recharge\Record as RechargeModel;
use app\admin\model\lottery\withdrawal\Record as WithdrawalModel;
use think\Db;

/**
 * 测试控制器
 */
class Test extends Backend
{
    /**
     * 测试用户名显示功能
     */
    public function username()
    {
        $result = [];
        
        // 测试订单列表用户关联
        $result['orders'] = [];
        try {
            $orders = OrderModel::with(['user' => function($query) {
                $query->field('id,username,nickname,mobile,remark');
            }])->limit(3)->select();
            
            foreach ($orders as $order) {
                $orderData = [
                    'id' => $order->id,
                    'user_id' => $order->user_id,
                    'user_info' => null
                ];
                
                if ($order->user) {
                    $orderData['user_info'] = [
                        'username' => $order->user->username,
                        'nickname' => $order->user->nickname,
                        'mobile' => $order->user->mobile
                    ];
                }
                
                $result['orders'][] = $orderData;
            }
        } catch (\Exception $e) {
            $result['orders_error'] = $e->getMessage();
        }
        
        // 测试充值记录用户关联
        $result['recharges'] = [];
        try {
            $recharges = RechargeModel::with(['user' => function($query) {
                $query->field('id,username,nickname,mobile,remark');
            }])->limit(3)->select();
            
            foreach ($recharges as $recharge) {
                $rechargeData = [
                    'id' => $recharge->id,
                    'user_id' => $recharge->user_id,
                    'user_info' => null
                ];
                
                if ($recharge->user) {
                    $rechargeData['user_info'] = [
                        'username' => $recharge->user->username,
                        'nickname' => $recharge->user->nickname,
                        'mobile' => $recharge->user->mobile
                    ];
                }
                
                $result['recharges'][] = $rechargeData;
            }
        } catch (\Exception $e) {
            $result['recharges_error'] = $e->getMessage();
        }
        
        // 测试提现记录用户关联
        $result['withdrawals'] = [];
        try {
            $withdrawals = WithdrawalModel::with(['user' => function($query) {
                $query->field('id,username,nickname,mobile,remark');
            }])->limit(3)->select();
            
            foreach ($withdrawals as $withdrawal) {
                $withdrawalData = [
                    'id' => $withdrawal->id,
                    'user_id' => $withdrawal->user_id,
                    'user_info' => null
                ];
                
                if ($withdrawal->user) {
                    $withdrawalData['user_info'] = [
                        'username' => $withdrawal->user->username,
                        'nickname' => $withdrawal->user->nickname,
                        'mobile' => $withdrawal->user->mobile
                    ];
                }
                
                $result['withdrawals'][] = $withdrawalData;
            }
        } catch (\Exception $e) {
            $result['withdrawals_error'] = $e->getMessage();
        }
        
        // 测试用户表数据
        try {
            $userCount = Db::name('user')->count();
            $users = Db::name('user')->field('id,username,nickname,mobile')->limit(3)->select();
            $result['user_table'] = [
                'count' => $userCount,
                'samples' => $users
            ];
        } catch (\Exception $e) {
            $result['user_table_error'] = $e->getMessage();
        }
        
        $this->success('测试完成', $result);
    }
}
