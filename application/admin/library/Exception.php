<?php

namespace app\admin\library;

use think\Request;
use think\Response;
use think\exception\HttpResponseException;

class Exception
{
    public function __construct($msg,$data=[],$code=0,$header=[])
    {
        $type = 'json';
        $result = [
            'code' => $code,
            'msg'  => $msg,
            'time' => Request::instance()->server('REQUEST_TIME'),
            'data' => $data,
        ];
        $response = Response::create($result, $type, $code)->header($header);
        throw new HttpResponseException($response);
    }

}
