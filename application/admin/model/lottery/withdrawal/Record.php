<?php

namespace app\admin\model\lottery\withdrawal;

use think\Model;


class Record extends Model
{

    

    

    // 表名
    protected $name = 'lottery_withdrawal_record';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'completetime_text',
        'status_text',
        'is_read_text',
        'payment_data_parsed',
        'payment_account_info'
    ];

    /**
     * 关联用户表
     */
    public function user()
    {
        return $this->hasOne('app\common\model\User', 'id', 'user_id');
    }
    
    
    public function getStatusList()
    {
        return ['0' => __('Status 0'), '1' => __('Status 1'), '2' => __('Status 2'), '3' => __('Status 3')];
    }

    public function getIsReadList()
    {
        return ['0' => __('Is_read 0'), '1' => __('Is_read 1')];
    }

    



    public function getCompletetimeTextAttr($value, $data)
    {
        $value = $value ?: ($data['completetime'] ?? '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    public function getStatusTextAttr($value, $data)
    {
        $value = $value ?: ($data['status'] ?? '');
        $list = $this->getStatusList();
        return $list[$value] ?? '';
    }

    public function getIsReadTextAttr($value, $data)
    {
        $value = $value ?: ($data['is_read'] ?? '');
        $list = $this->getIsReadList();
        return $list[$value] ?? '';
    }

    protected function setCompletetimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    /**
     * 解析 payment_data JSON 字段
     */
    public function getPaymentDataParsedAttr($value, $data)
    {
        if (empty($data['payment_data'])) {
            return null;
        }

        $paymentData = json_decode($data['payment_data'], true);
        return $paymentData ?: null;
    }

    /**
     * 获取收款账户信息（用于后台显示）
     */
    public function getPaymentAccountInfoAttr($value, $data)
    {
        // 优先使用 payment_data 快照
        if (!empty($data['payment_data'])) {
            $paymentData = json_decode($data['payment_data'], true);
            if ($paymentData) {
                return [
                    'source' => $paymentData['source'] ?? 'unknown',
                    'method_name' => $paymentData['method_name'] ?? '',
                    'method_type' => $paymentData['method_type'] ?? '',
                    'account_name' => $paymentData['account_name'] ?? '',
                    'account_number' => $paymentData['account_number'] ?? '',
                    'bank_name' => $paymentData['bank_name'] ?? '',
                    'bank_branch' => $paymentData['bank_branch'] ?? '',
                    'qr_code_url' => $paymentData['qr_code_url'] ?? '',
                    'snapshot_time' => $paymentData['snapshot_time'] ?? null,
                    'snapshot_source' => $paymentData['snapshot_source'] ?? 'unknown',
                    'is_snapshot' => true
                ];
            }
        }

        return [
            'source' => 'no_data',
            'method_name' => '无收款信息',
            'method_type' => '',
            'account_name' => '',
            'account_number' => '',
            'bank_name' => '',
            'bank_branch' => '',
            'qr_code_url' => '',
            'snapshot_time' => null,
            'snapshot_source' => 'none',
            'is_snapshot' => false
        ];
    }


}
