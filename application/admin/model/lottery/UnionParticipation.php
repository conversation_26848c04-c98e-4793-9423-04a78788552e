<?php

namespace app\admin\model\lottery;

use think\Model;

class UnionParticipation extends Model
{
    // 表名
    protected $name = 'lottery_union_participation';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';

    // 追加属性
    protected $append = [
        'status_text',
        'createtime_text',
        'user_display'
    ];
    
    /**
     * 获取状态列表
     */
    public function getStatusList()
    {
        return [
            '0' => '未支付',
            '1' => '已支付'
        ];
    }
    
    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $status = $data['status'] ?? 0;
        $statusList = $this->getStatusList();
        return $statusList[$status] ?? '';
    }
    
    /**
     * 获取创建时间文本
     */
    public function getCreatetimeTextAttr($value, $data)
    {
        $createtime = $data['createtime'] ?? 0;
        return $createtime ? date('Y-m-d H:i:s', $createtime) : '';
    }
    
    /**
     * 获取用户显示名
     */
    public function getUserDisplayAttr($value, $data)
    {
        if (isset($data['user'])) {
            $user = $data['user'];
            $display = $user['nickname'] ?: $user['username'];
            if ($user['mobile']) {
                $display .= ' (' . substr($user['mobile'], 0, 3) . '****' . substr($user['mobile'], -4) . ')';
            }
            return $display;
        }
        return '';
    }
    
    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo('app\common\model\User', 'user_id', 'id')
            ->field('id,username,nickname,mobile');
    }
    
    /**
     * 关联合买方案
     */
    public function unionPlan()
    {
        return $this->belongsTo('app\admin\model\lottery\UnionPlan', 'union_plan_id', 'id');
    }
}
