<?php

namespace app\admin\model\lottery\match;

use think\Model;
use traits\model\SoftDelete;

class Football extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'lottery_match_football';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'match_time_text'
    ];
    

    



    public function getMatchTimeTextAttr($value, $data)
    {
        $value = $value ?: ($data['match_time'] ?? '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    protected function setMatchTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }


}
