<?php

namespace app\admin\model\lottery\statistics;

use think\Model;
use think\Db;

class Index extends Model
{

    // 表名
    protected $name = 'lottery_order';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    /**
     * 获取概览统计
     */
    public function getOverviewStats($storeId = '', $startTime = '', $endTime = '')
    {
        $query = $this->where('deletetime', null);
        
        if ($storeId) {
            $query->where('store_id', $storeId);
        }
        
        if ($startTime && $endTime) {
            $query->whereBetween('createtime', [$startTime, $endTime]);
        }
        
        $result = $query->field([
            'COUNT(*) as total_orders',
            'COUNT(DISTINCT user_id) as total_users', 
            'SUM(total_amount) as total_bet_amount',
            'SUM(prize_amount) as total_prize_amount',
            'SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as winning_orders',
            'AVG(total_amount) as avg_bet_amount'
        ])->find();
        
        if ($result) {
            // 计算盈亏
            $result['profit_loss'] = ($result['total_bet_amount'] ?: 0) - ($result['total_prize_amount'] ?: 0);
            $result['win_rate'] = ($result['total_orders'] ?: 0) > 0 ? round(($result['winning_orders'] ?: 0) / $result['total_orders'] * 100, 2) : 0;
            return $result;
        }
        
        return [
            'total_orders' => 0,
            'total_users' => 0,
            'total_bet_amount' => 0,
            'total_prize_amount' => 0,
            'winning_orders' => 0,
            'avg_bet_amount' => 0,
            'profit_loss' => 0,
            'win_rate' => 0
        ];
    }
    
    /**
     * 获取店铺统计
     */
    public function getStoreStats($storeId = '', $startTime = '', $endTime = '')
    {
        $storeModel = model('admin/lottery/store/Index');
        
        $query = $this->alias('o')
            ->join([$storeModel->getTable() => 's'], 'o.store_id = s.id', 'LEFT')
            ->where('o.deletetime', null);
        
        if ($storeId) {
            $query->where('o.store_id', $storeId);
        }
        
        if ($startTime && $endTime) {
            $query->whereBetween('o.createtime', [$startTime, $endTime]);
        }
        
        return $query->field([
            'o.store_id',
            's.name as store_name',
            'COUNT(*) as order_count',
            'SUM(o.total_amount) as total_amount',
            'SUM(o.prize_amount) as total_prize',
            'SUM(o.total_amount) - SUM(o.prize_amount) as profit',
            'COUNT(DISTINCT o.user_id) as user_count'
        ])
        ->group('o.store_id')
        ->order('total_amount DESC')
        ->select();
    }
    
    /**
     * 获取彩种统计
     */
    public function getLotteryTypeStats($storeId = '', $startTime = '', $endTime = '')
    {
        $query = $this->where('deletetime', null);
        
        if ($storeId) {
            $query->where('store_id', $storeId);
        }
        
        if ($startTime && $endTime) {
            $query->whereBetween('createtime', [$startTime, $endTime]);
        }
        
        return $query->field([
            'lottery_type',
            'COUNT(*) as order_count',
            'SUM(total_amount) as total_amount',
            'SUM(prize_amount) as total_prize',
            'AVG(total_amount) as avg_amount'
        ])
        ->group('lottery_type')
        ->order('total_amount DESC')
        ->select();
    }
    
    /**
     * 获取每日统计
     */
    public function getDailyStats($storeId = '', $startTime = '', $endTime = '')
    {
        if (!$startTime || !$endTime) {
            $endTime = time();
            $startTime = $endTime - 30 * 24 * 3600; // 默认30天
        }
        
        $query = $this->where('deletetime', null)
            ->whereBetween('createtime', [$startTime, $endTime]);
        
        if ($storeId) {
            $query->where('store_id', $storeId);
        }
        
        return $query->field([
            'DATE(FROM_UNIXTIME(createtime)) as date',
            'COUNT(*) as order_count',
            'SUM(total_amount) as total_amount',
            'SUM(prize_amount) as total_prize',
            'COUNT(DISTINCT user_id) as active_users'
        ])
        ->group('DATE(FROM_UNIXTIME(createtime))')
        ->order('date')
        ->select();
    }
    
    /**
     * 获取用户统计
     */
    public function getUserStats($storeId = '', $startTime = '', $endTime = '')
    {
        $userModel = model('admin/User');
        $query = $userModel->where('status', 'normal');
        
        if ($storeId) {
            $query->where('store_id', $storeId);
        }
        
        if ($startTime && $endTime) {
            $query->whereBetween('createtime', [$startTime, $endTime]);
        }
        
        $todayStart = strtotime(date('Y-m-d 00:00:00'));
        
        $result = $query->field([
            'COUNT(*) as total_users',
            "SUM(CASE WHEN createtime >= {$todayStart} THEN 1 ELSE 0 END) as new_users_today",
            "SUM(CASE WHEN logintime >= {$todayStart} THEN 1 ELSE 0 END) as active_users_today",
            'AVG(money) as avg_balance',
            'SUM(money) as total_balance'
        ])->find();
        
        return $result ?: [
            'total_users' => 0,
            'new_users_today' => 0,
            'active_users_today' => 0,
            'avg_balance' => 0,
            'total_balance' => 0
        ];
    }
    
    /**
     * 获取充值统计
     */
    public function getRechargeStats($storeId = '', $startTime = '', $endTime = '')
    {
        try {
            $rechargeModel = model('admin/lottery/recharge/Record');
            $query = $rechargeModel->where('deletetime', null)->where('status', 1);
            
            if ($storeId) {
                $query->where('store_id', $storeId);
            }
            
            if ($startTime && $endTime) {
                $query->whereBetween('createtime', [$startTime, $endTime]);
            }
            
            $result = $query->field([
                'COUNT(*) as recharge_count',
                'SUM(amount) as total_recharge',
                'SUM(fee_amount) as total_recharge_fee'
            ])->find();
            
            return $result ?: [
                'recharge_count' => 0,
                'total_recharge' => 0,
                'total_recharge_fee' => 0
            ];
        } catch (\Exception $e) {
            return [
                'recharge_count' => 0,
                'total_recharge' => 0,
                'total_recharge_fee' => 0
            ];
        }
    }
    
    /**
     * 获取提现统计
     */
    public function getWithdrawalStats($storeId = '', $startTime = '', $endTime = '')
    {
        try {
            $withdrawalModel = model('admin/lottery/withdrawal/Record');
            $query = $withdrawalModel->where('status', 1);
            
            if ($storeId) {
                $query->where('store_id', $storeId);
            }
            
            if ($startTime && $endTime) {
                $query->whereBetween('createtime', [$startTime, $endTime]);
            }
            
            $result = $query->field([
                'COUNT(*) as withdrawal_count',
                'SUM(amount) as total_withdrawal',
                'SUM(fee) as total_withdrawal_fee'
            ])->find();
            
            return $result ?: [
                'withdrawal_count' => 0,
                'total_withdrawal' => 0,
                'total_withdrawal_fee' => 0
            ];
        } catch (\Exception $e) {
            return [
                'withdrawal_count' => 0,
                'total_withdrawal' => 0,
                'total_withdrawal_fee' => 0
            ];
        }
    }
    
    /**
     * 获取财务统计
     */
    public function getFinanceStats($storeId = '', $startTime = '', $endTime = '')
    {
        $rechargeStats = $this->getRechargeStats($storeId, $startTime, $endTime);
        $withdrawalStats = $this->getWithdrawalStats($storeId, $startTime, $endTime);
        
        return [
            'recharge' => $rechargeStats,
            'withdrawal' => $withdrawalStats
        ];
    }

    /**
     * 获取会员资金统计概览
     */
    public function getMemberFundOverview($storeId = '', $startTime = '', $endTime = '')
    {
        $userModel = model('admin/User');
        $query = $userModel->where('status', 'normal');

        if ($storeId) {
            $query->where('store_id', $storeId);
        }

        // 总会员数
        $totalMembers = $query->count();

        // 总充值额（从充值记录表统计已完成的充值）
        $rechargeQuery = Db::name('lottery_recharge_record')->alias('rr')
            ->where('rr.status', 4) // 已完成状态
            ->where('rr.deletetime', null);
        if ($storeId) {
            $rechargeQuery->join(['sys_user' => 'u'], 'rr.user_id = u.id')
                ->where('u.store_id', $storeId);
        }
        if ($startTime && $endTime) {
            $rechargeQuery->whereBetween('rr.createtime', [$startTime, $endTime]);
        }
        $totalRecharge = $rechargeQuery->sum('rr.amount') ?: 0;

        // 总提现额（从提现记录表统计已完成的提现）
        $withdrawalQuery = Db::name('lottery_withdrawal_record')->alias('wr')
            ->where('wr.status', 3); // 已完成状态（根据数据分析，状态3是已完成）
        if ($storeId) {
            $withdrawalQuery->join(['sys_user' => 'u'], 'wr.user_id = u.id')
                ->where('u.store_id', $storeId);
        }
        if ($startTime && $endTime) {
            $withdrawalQuery->whereBetween('wr.createtime', [$startTime, $endTime]);
        }
        $totalWithdrawal = $withdrawalQuery->sum('wr.amount') ?: 0;

        // 总中奖额（从彩票订单表统计已派奖订单）
        $prizeQuery = $this->where('status', 4); // 已派奖状态
        if ($storeId) {
            $prizeQuery->where('store_id', $storeId);
        }
        if ($startTime && $endTime) {
            $prizeQuery->whereBetween('createtime', [$startTime, $endTime]);
        }
        $totalPrize = $prizeQuery->sum('awarded_amount') ?: 0;

        return [
            'total_members' => $totalMembers,
            'total_recharge' => $totalRecharge,
            'total_withdrawal' => $totalWithdrawal,
            'total_prize' => $totalPrize
        ];
    }

    /**
     * 获取会员资金统计详细数据
     */
    public function getMemberFundStats($storeId = '', $search = '', $page = 1, $limit = 20, $sortField = 'register_time', $sortOrder = 'desc')
    {
        $userModel = model('admin/User');
        $query = $userModel->alias('u')
            ->where('u.status', 'normal');

        // 店铺权限过滤
        if ($storeId) {
            $query->where('u.store_id', $storeId);
        }

        // 搜索条件
        if ($search) {
            $query->where(function($q) use ($search) {
                $q->whereOr('u.username', 'like', "%{$search}%")
                  ->whereOr('u.mobile', 'like', "%{$search}%");
            });
        }

        // 排序
        $allowedSortFields = ['register_time', 'username', 'mobile', 'current_balance', 'total_recharge', 'total_withdrawal', 'total_prize'];
        if (!in_array($sortField, $allowedSortFields)) {
            $sortField = 'register_time';
        }

        if ($sortField === 'register_time') {
            $query->order('u.createtime', $sortOrder);
        } else {
            // 对于统计字段，需要在子查询中处理排序
            $query->order('u.id', 'desc'); // 默认排序，后续会重新排序
        }

        // 分页
        $offset = ($page - 1) * $limit;
        $users = $query->field('u.id, u.username, u.mobile, u.money as current_balance, u.createtime as register_time, u.remark')
            ->limit($offset, $limit)
            ->select();

        $total = $query->count();

        // 为每个用户计算统计数据
        $list = [];
        foreach ($users as $user) {
            $userId = $user['id'];

            // 充值总额（从充值记录表统计已完成的充值）
            $totalRecharge = Db::name('lottery_recharge_record')
                ->where('user_id', $userId)
                ->where('status', 4) // 已完成状态
                ->where('deletetime', null)
                ->sum('amount') ?: 0;

            // 提现总额（从提现记录表统计已完成的提现）
            $totalWithdrawal = Db::name('lottery_withdrawal_record')
                ->where('user_id', $userId)
                ->where('status', 3) // 已完成状态
                ->sum('amount') ?: 0;

            // 中奖总额（已派奖订单的实际派奖金额）
            $totalPrize = $this->where('user_id', $userId)
                ->where('status', 4) // 已派奖状态
                ->sum('awarded_amount') ?: 0;

            $list[] = [
                'user_id' => $userId,
                'username' => $user['username'],
                'mobile' => $user['mobile'],
                'current_balance' => floatval($user['current_balance']),
                'total_recharge' => floatval($totalRecharge),
                'total_withdrawal' => floatval($totalWithdrawal),
                'total_prize' => floatval($totalPrize),
                'register_time' => $user['register_time'],
                'remark' => $user['remark'] ?: ''
            ];
        }

        // 如果需要按统计字段排序，在这里重新排序
        if (in_array($sortField, ['total_recharge', 'total_withdrawal', 'total_prize', 'current_balance'])) {
            usort($list, function($a, $b) use ($sortField, $sortOrder) {
                $result = $a[$sortField] <=> $b[$sortField];
                return $sortOrder === 'desc' ? -$result : $result;
            });
        }

        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit
        ];
    }

}