<?php

namespace app\admin\model\lottery\order;

use think\Model;
use traits\model\SoftDelete;

class Qxc extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'lottery_order_qxc';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'bet_type_text',
        'settletime_text'
    ];
    

    
    public function getBetTypeList()
    {
        return ['ds' => '单式投注', 'fs' => '复式投注', 'dt' => '胆拖投注'];
    }


    public function getBetTypeTextAttr($value, $data)
    {
        $value = $value ?: ($data['bet_type'] ?? '');
        $list = $this->getBetTypeList();
        return $list[$value] ?? '';
    }


    public function getSettletimeTextAttr($value, $data)
    {
        $value = $value ?: ($data['settletime'] ?? '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    protected function setSettletimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    // bet_data
    public function getBetDataAttr($value, $data)
    {
        return json_decode($value, true);
    }


}
