<?php

namespace app\admin\model\lottery\order;

use think\Model;
use traits\model\SoftDelete;

class Dlt extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'lottery_order_dlt';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'settletime_text',
        'bet_type_text'
    ];
    

    



    public function getSettletimeTextAttr($value, $data)
    {
        $value = $value ?: ($data['settletime'] ?? '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    protected function setSettletimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    // bet_data
    public function getBetDataAttr($value, $data)
    {
        return json_decode($value, true);
    }

    // bet_type_text
    public function getBetTypeTextAttr($value, $data)
    {
        $betType = isset($data['bet_type']) ? $data['bet_type'] : '';
        $list = $this->getBetTypeList();
        return isset($list[$betType]) ? $list[$betType] : $betType;
    }

    // 投注类型列表
    public function getBetTypeList()
    {
        return [
            'normal' => '普通投注',
            'dan_tuo' => '胆拖投注'
        ];
    }


}
