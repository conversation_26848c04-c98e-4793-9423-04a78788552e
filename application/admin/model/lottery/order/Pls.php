<?php

namespace app\admin\model\lottery\order;

use think\Model;
use traits\model\SoftDelete;

class Pls extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'lottery_order_pls';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'bet_type_text',
        'settletime_text'
    ];
    

    
    public function getBetTypeList()
    {
        return [
            'zx_dwfs' => '直选定位复式',
            'zx_zh3bt' => '直选组合3包通',
            'zx_hz' => '直选和值',
            'zx_zhdt' => '直选组合胆拖',
            'zx_kdfs' => '直选跨度复式',
            'zu3_ds' => '组三单式',
            'zu3_fs' => '组三复式',
            'zu3_dt' => '组三胆拖',
            'zu6_fs' => '组六复式',
            'zu6_dt' => '组六胆拖',
            'zu6_kdfs' => '组六跨度复式',
            'zux_hz' => '组选和值',
            'zux_2qb' => '组选2期包通'
        ];
    }


    public function getBetTypeTextAttr($value, $data)
    {
        $value = $value ?: ($data['bet_type'] ?? '');
        $list = $this->getBetTypeList();
        return $list[$value] ?? '';
    }


    public function getSettletimeTextAttr($value, $data)
    {
        $value = $value ?: ($data['settletime'] ?? '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    protected function setSettletimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    // bet_data
    public function getBetDataAttr($value, $data)
    {
        return json_decode($value, true);
    }


}
