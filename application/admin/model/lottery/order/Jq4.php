<?php

namespace app\admin\model\lottery\order;

use think\Model;
use traits\model\SoftDelete;

class Jq4 extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'lottery_order_jq4';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'paytime_text',
        'settletime_text'
    ];
    

    



    public function getPaytimeTextAttr($value, $data)
    {
        $value = $value ?: ($data['paytime'] ?? '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getSettletimeTextAttr($value, $data)
    {
        $value = $value ?: ($data['settletime'] ?? '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    protected function setPaytimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setSettletimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    // bet_data
    public function getBetDataAttr($value, $data)
    {
        return json_decode($value, true);
    }


}
