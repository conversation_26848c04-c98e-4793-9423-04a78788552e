<?php

namespace app\admin\model\lottery\order;

use think\Model;
use think\Db;
use app\admin\library\Exception;
use traits\model\SoftDelete;
use app\common\model\User;

class Index extends Model
{

    // 表名
    protected $name = 'lottery_order';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';

    // 追加属性
    protected $append = [
        'lottery_type_text',
    ];

    /**
     * 关联用户表
     */
    public function user()
    {
        return $this->hasOne('app\common\model\User', 'id', 'user_id');
    }



    /**
     * 出票操作
     * 更新订单的票据图片和状态
     *
     * @param int $id 订单ID
     * @param string $ticket_images 票据图片URL，多张用逗号分隔
     * @return bool
     * @throws Exception
     */
    public function ticket($id, $ticket_images)
    {
        if (empty($id)) {
            new Exception('订单ID不能为空');
        }

        if (empty($ticket_images)) {
            new Exception('票据图片不能为空');
        }

        // 查询订单
        $order = $this->where('id', $id)->find();
        if (!$order) {
            new Exception('订单不存在');
        }

        // 验证订单状态
        if ($order['status'] != 1) {
            new Exception('只有待出票状态的订单才能进行出票操作');
        }

        // 开启事务
        Db::startTrans();
        try {
            // 更新订单状态和票据图片
            $updateData = [
                'ticket_images' => $ticket_images,
                'status' => 2, // 更新为待开奖状态
                'updatetime' => time()
            ];

            $result = $this->where('id', $id)->update($updateData);
            if (!$result) {
                new Exception('更新订单失败');
            }

            // 提交事务
            Db::commit();
            return true;
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            new Exception('出票失败：' . $e->getMessage());
        }
    }

    /**
     * 更新投注赔率
     *
     * @param int $id 订单ID
     * @param string $bet_data 新的投注数据JSON字符串
     * @return bool
     * @throws Exception
     */
    public function updateOdds($id, $bet_data)
    {
        if (empty($id)) {
            new Exception('订单ID不能为空');
        }

        if (empty($bet_data)) {
            new Exception('投注数据不能为空');
        }

        // 查询订单
        $order = $this->where('id', $id)->find();
        if (!$order) {
            new Exception('订单不存在');
        }

        // 验证订单状态 - 只有待出票(1)和待开奖(2)状态可以修改赔率
        if (!in_array($order['status'], [1, 2])) {
            new Exception('只有待出票或待开奖状态的订单才能修改赔率');
        }

        // 验证是否为支持的竞彩订单类型
        if (!in_array($order['lottery_type'], ['football', 'basketball'])) {
            new Exception('只有足球和篮球订单支持修改赔率');
        }

        // 开启事务
        Db::startTrans();
        try {
            $lottery_type = $order['lottery_type'];

            // 查找对应的子订单
            $subOrder = Db::name('lottery_order_' . $lottery_type)
                ->where('order_id', $id)
                ->find();

            if (!$subOrder) {
                new Exception('找不到对应的' . ($lottery_type == 'football' ? '足球' : '篮球') . '订单数据');
            }

            // 重新计算潜在奖金
            $bet_method = $subOrder['bet_method'];
            $bet_multiple = $subOrder['bet_multiple'];

            // 引入对应的计算类
            $libraryClass = '\\addons\\lottery\\library\\order\\' . ucfirst($lottery_type);
            if (!class_exists($libraryClass)) {
                new Exception($lottery_type . '计算类不存在');
            }

            // 计算新的潜在奖金
            $potentialPrize = $libraryClass::calculatePotentialPrize($bet_data, $bet_method, $bet_multiple);
            $newPotentialWin = isset($potentialPrize['max']) ? $potentialPrize['max'] : 0;

            // 更新子订单的bet_data
            $result = Db::name('lottery_order_' . $lottery_type)
                ->where('order_id', $id)
                ->update([
                    'bet_data' => $bet_data,
                    'updatetime' => time()
                ]);

            if (!$result) {
                new Exception('更新投注数据失败');
            }

            // 更新主订单的潜在奖金和更新时间
            $mainUpdateResult = $this->where('id', $id)->update([
                'potential_win' => $newPotentialWin,
                'updatetime' => time()
            ]);

            if (!$mainUpdateResult) {
                new Exception('更新主订单潜在奖金失败');
            }

            // 更新订单明细表
            $itemTableName = 'lottery_order_item_' . $lottery_type;
            $existingItems = Db::name($itemTableName)
                ->where('order_id', $id)
                ->select();

            if (!empty($existingItems)) {
                // 解析新的bet_data，建立赔率映射
                $newBetDataArray = is_string($bet_data) ? json_decode($bet_data, true) : $bet_data;
                $oddsMap = [];
                foreach ($newBetDataArray as $bet) {
                    $key = $bet['match_id'] . '_' . $bet['option_type'];
                    $oddsMap[$key] = $bet['odds'];
                }

                // 更新每个订单明细的bet_data中的赔率
                foreach ($existingItems as $item) {
                    $itemBetData = is_string($item['bet_data']) ? json_decode($item['bet_data'], true) : $item['bet_data'];

                    // 更新该item中每个选择的赔率
                    foreach ($itemBetData as &$bet) {
                        $key = $bet['match_id'] . '_' . $bet['option_type'];
                        if (isset($oddsMap[$key])) {
                            $bet['odds'] = $oddsMap[$key];
                        }
                    }

                    // 保存更新后的bet_data
                    $updateResult = Db::name($itemTableName)
                        ->where('id', $item['id'])
                        ->update([
                            'bet_data' => json_encode($itemBetData, JSON_UNESCAPED_UNICODE),
                            'updatetime' => time()
                        ]);

                    if (!$updateResult) {
                        new Exception('更新订单明细失败');
                    }
                }
            }

            // 提交事务
            Db::commit();
            return true;
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            new Exception('更新赔率失败：' . $e->getMessage());
        }
    }

    /**
     * 更新足球投注赔率（便捷方法）
     *
     * @param int $id 订单ID
     * @param string $bet_data 新的投注数据JSON字符串
     * @return bool
     */
    public function updateFootballOdds($id, $bet_data)
    {
        // 验证是否为足球订单
        $order = $this->where('id', $id)->find();
        if (!$order) {
            new Exception('订单不存在');
        }

        if ($order['lottery_type'] !== 'football') {
            new Exception('订单类型不是足球');
        }

        return $this->updateOdds($id, $bet_data);
    }

    /**
     * 更新篮球投注赔率（便捷方法）
     *
     * @param int $id 订单ID
     * @param string $bet_data 新的投注数据JSON字符串
     * @return bool
     */
    public function updateBasketballOdds($id, $bet_data)
    {
        // 验证是否为篮球订单
        $order = $this->where('id', $id)->find();
        if (!$order) {
            new Exception('订单不存在');
        }

        if ($order['lottery_type'] !== 'basketball') {
            new Exception('订单类型不是篮球');
        }

        return $this->updateOdds($id, $bet_data);
    }

    /**
     * 派奖操作
     * 为已中奖的订单派发奖金并增加用户余额
     *
     * @param int $id 订单ID
     * @param float $prize_amount 奖金金额
     * @param float $tax_amount 税额
     * @return bool
     * @throws Exception
     */
    public function awardPrize($id, $prize_amount, $tax_amount)
    {
        if (empty($id)) {
            new Exception('订单ID不能为空');
        }

        if ($prize_amount < 0 || $tax_amount < 0) {
            new Exception('奖金和税额不能为负数');
        }

        if ($prize_amount <= $tax_amount) {
            new Exception('奖金金额必须大于税额');
        }

        // 查询订单
        $order = $this->where('id', $id)->find();
        if (!$order) {
            new Exception('订单不存在');
        }

        // 验证订单状态 - 使用新的状态定义
        if ($order['status'] != 3) { // STATUS_WON_PENDING_PAYOUT = 3
            new Exception('只能对已中奖待派奖的订单进行派奖');
        }

        // 检查是否已经派奖
        if ($order['awarded_amount'] > 0) {
            new Exception('该订单已经派奖，不能重复派奖');
        }

        // 计算实际到账金额
        $actual_amount = $prize_amount - $tax_amount;

        // 开启事务
        Db::startTrans();
        try {
            // 1. 更新订单的奖金、税额、实际派奖金额和状态
            $orderUpdateResult = $this->where('id', $id)->update([
                'prize_amount' => $prize_amount,
                'tax_amount' => $tax_amount,
                'awarded_amount' => $actual_amount,
                'status' => 4, // STATUS_PAID_OUT = 4 (已派奖)
                'updatetime' => time()
            ]);

            if (!$orderUpdateResult) {
                new Exception('更新订单奖金信息失败');
            }

            // 2. 使用User::money方法增加用户余额（该方法已包含余额更新和资金流水记录）
            $memo = '订单派奖，订单号：' . $order['order_no'] . '，奖金：' . $prize_amount . '，税额：' . $tax_amount;
            User::money($actual_amount, $order['user_id'], $memo);

            // 提交事务
            Db::commit();
            return true;
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            new Exception('派奖失败：' . $e->getMessage());
        }

        return false;
    }

    /**
     * 退单操作
     * 为符合条件的订单执行退单并退还用户金额
     *
     * @param int $id 订单ID
     * @param string $refund_reason 退单原因
     * @return bool
     * @throws Exception
     */
    public function refundOrder($id, $refund_reason = '')
    {
        if (empty($id)) {
            new Exception('订单ID不能为空');
        }

        // 查询订单
        $order = $this->where('id', $id)->find();
        if (!$order) {
            new Exception('订单不存在');
        }

        // 验证订单状态 - 只有待出票(1)和待开奖(2)状态可以退单
        if (!in_array($order['status'], [1, 2])) {
            $statusTexts = [
                0 => '未付款',
                1 => '待出票', 
                2 => '待开奖',
                3 => '已中奖',
                4 => '已派奖',
                5 => '未中奖',
                8 => '已退单',
                9 => '已取消'
            ];
            $currentStatusText = $statusTexts[$order['status']] ?? '未知状态';
            new Exception("当前订单状态为【{$currentStatusText}】，只有【待出票】和【待开奖】状态的订单可以退单");
        }

        // 检查订单金额
        if ($order['total_amount'] <= 0) {
            new Exception('订单金额异常，无法退单');
        }

        // 计算退单金额（全额退还）
        $refund_amount = $order['total_amount'];

        // 开启事务
        Db::startTrans();
        try {
            // 1. 更新主订单状态为已退单，记录退单信息
            $orderUpdateResult = $this->where('id', $id)->update([
                'status' => 8, // 已退单状态
                'refund_amount' => $refund_amount,
                'refund_time' => time(),
                'refund_reason' => $refund_reason ?: '管理员退单',
                'updatetime' => time()
            ]);

            if (!$orderUpdateResult) {
                new Exception('更新订单状态失败');
            }

            // 2. 更新对应的子订单状态（如果存在）
            if (!empty($order['lottery_type'])) {
                $subOrderTable = 'lottery_order_' . $order['lottery_type'];
                $subOrderExists = Db::name($subOrderTable)->where('order_id', $id)->find();
                
                if ($subOrderExists) {
                    $subOrderUpdateResult = Db::name($subOrderTable)
                        ->where('order_id', $id)
                        ->update([
                            'status' => 8, // 已退单状态
                            'updatetime' => time()
                        ]);
                    
                    if (!$subOrderUpdateResult) {
                        new Exception('更新子订单状态失败');
                    }
                }
            }

            // 3. 使用User::money方法增加用户余额（退还订单金额）
            $memo = '订单退单，订单号：' . $order['order_no'] . '，退单金额：' . $refund_amount;
            User::money($refund_amount, $order['user_id'], $memo);

            // 提交事务
            Db::commit();
            return true;
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            new Exception('退单失败：' . $e->getMessage());
        }

        return false;
    }

    /**
     * 批量退单操作
     * 
     * @param array $ids 订单ID数组
     * @param string $refund_reason 退单原因
     * @return array 返回成功和失败的详细信息
     */
    public function batchRefundOrders($ids, $refund_reason = '')
    {
        if (empty($ids) || !is_array($ids)) {
            new Exception('订单ID数组不能为空');
        }

        $successCount = 0;
        $failedOrders = [];

        foreach ($ids as $id) {
            try {
                $this->refundOrder($id, $refund_reason);
                $successCount++;
            } catch (\Exception $e) {
                $failedOrders[] = [
                    'id' => $id,
                    'error' => $e->getMessage()
                ];
            }
        }

        return [
            'success_count' => $successCount,
            'failed_orders' => $failedOrders,
            'total_count' => count($ids)
        ];
    }

    /**
     * 创建足球订单明细
     *
     * @param int $orderId 主订单ID
     * @param int $footballOrderId 足球子订单ID
     * @param string $betData 投注数据JSON字符串
     * @param array $footballOrder 足球订单数据
     * @return bool
     */
    private function createFootballOrderItems($orderId, $footballOrderId, $betData, $footballOrder)
    {
        // 解析投注数据
        $betDataArray = is_string($betData) ? json_decode($betData, true) : $betData;
        if (empty($betDataArray)) {
            return true; // 没有数据时直接返回成功
        }

        // 解析投注方式
        $betMethod = is_string($footballOrder['bet_method']) ?
            json_decode($footballOrder['bet_method'], true) : $footballOrder['bet_method'];

        // 按比赛分组投注数据
        $matchGroups = [];
        foreach ($betDataArray as $bet) {
            $matchId = $bet['match_id'];
            if (!isset($matchGroups[$matchId])) {
                $matchGroups[$matchId] = [
                    'match_id' => $matchId,
                    'team1_name' => $bet['team1_name'],
                    'team2_name' => $bet['team2_name'],
                    'matchNum' => $bet['matchNum'],
                    'matchWeekDay' => $bet['matchWeekDay'],
                    'options' => []
                ];
            }
            $matchGroups[$matchId]['options'][] = $bet;
        }

        // 为每个投注方式创建订单明细
        foreach ($betMethod as $method) {
            $itemData = [
                'order_id' => $orderId,
                'order_football_id' => $footballOrderId,
                'store_id' => $footballOrder['store_id'],
                'user_id' => $footballOrder['user_id'],
                'match_ids' => $footballOrder['match_ids'],
                'guan_type' => $method,
                'bet_type' => $footballOrder['bet_type'],
                'bet_data' => $betData,
                'bet_multiple' => $footballOrder['bet_multiple'],
                'status' => 0,
                'prize_amount' => 0.00,
                'createtime' => time(),
                'updatetime' => time()
            ];

            $result = Db::name('lottery_order_item_football')->insert($itemData);
            if (!$result) {
                new Exception('创建订单明细失败');
            }
        }

        return true;
    }



    public function getLotteryTypeList()
    {
        return ['football' => __('Football'), 'dlt' => __('Dlt'), 'jq4' => __('Jq4'), 'bqc6' => __('Bqc6'), 'pls' => __('Pls'), 'plw' => __('Plw'), 'qxc' => __('Qxc'), 'rx9' => __('Rx9'), 'sfc' => __('Sfc'), 'basketball' => __('Basketball')];
    }

    // getStatusList
    public function getStatusList(){
        // 状态：0未付款，1待出票，2待开奖，3已中奖，4已派奖，5未中奖，8已退单，9已取消
        return ['0'=> __('Status 0') ,  '1'=> __('Status 1') , '2'=> __('Status 2') , '3'=> __('Status 3') , '4'=> __('Status 4') , '5'=> __('Status 5'), '6'=> __('Status 6'),  '8'=> __('Status 8'), '9'=> __('Status 9')  ];
    }

    /**
     * 获取合买状态列表
     */
    public function getUnionStatusList()
    {
        return [
            '0' => '非合买',
            '1' => '认购中',
            '2' => '已满员',
        ];
    }

    /**
     * 获取订单类型列表（基于union字段）
     */
    public function getUnionList()
    {
        return [
            '0' => '普通订单',
            '1' => '合买订单'
        ];
    }


    public function getLotteryTypeTextAttr($value, $data)
    {
        $value = $value ?: ($data['lottery_type'] ?? '');
        $list = $this->getLotteryTypeList();
        return $list[$value] ?? '';
    }

//    // getSubOrder
//    public function getSubOrderAttr($value, $data)
//    {
//        $subOrder = [];
//        if (!empty($data['lottery_type'])) {
//            // 动态构造子订单模型类名
//            $subOrderClass = "\\app\\admin\\model\\lottery\\order\\" . ucfirst($data['lottery_type']);
//
//            if (class_exists($subOrderClass)) {
//                $subOrderModel = new $subOrderClass();
//                $subOrder = $subOrderModel->where('order_id', $data['id'])->find();
//            }
//        }
//        return $subOrder;
//    }
//
//    // getSubOrderItems
//    public function getSubOrderItemsAttr($value, $data)
//    {
//        $orderItems = [];
//        if (!empty($data['lottery_type'])) {
//            // 动态构造订单项模型类名
//            $itemOrderClass = "\\app\\admin\\model\\lottery\\order\\item\\" . ucfirst($data['lottery_type']);
//            if (class_exists($itemOrderClass)) {
//                $itemOrderModel = new $itemOrderClass();
//                $orderItems = $itemOrderModel->where('order_id', $data['id'])->select();
//            }
//
//        }
//        return $orderItems;
//    }

    /**
     * 关联合买方案
     */
    public function unionPlan()
    {
        return $this->hasOne('app\admin\model\lottery\UnionPlan', 'order_id', 'id');
    }

    /**
     * 获取合买状态文本
     */
    public function getUnionStatusTextAttr($value, $data)
    {
        $unionStatus = $data['union_status'] ?? 0;
        $unionStatusList = $this->getUnionStatusList();
        return $unionStatusList[$unionStatus] ?? '';
    }




}
