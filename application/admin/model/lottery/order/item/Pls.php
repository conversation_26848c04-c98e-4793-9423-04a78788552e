<?php

namespace app\admin\model\lottery\order\item;

use think\Model;
use traits\model\SoftDelete;

class Pls extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'lottery_order_item_pls';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [

    ];
    

    







    // bet_data
    public function getBetDataAttr($value, $data)
    {
        return json_decode($value, true);
    }

    // numbers
    public function getNumbersAttr($value, $data)
    {
        if (empty($value)) {
            return [];
        }
        return explode(',', $value);
    }

}
