<?php

namespace app\admin\model\lottery\order\item;

use think\Model;
use traits\model\SoftDelete;

class Football extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'lottery_order_item_football';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'bet_type_text',
        'status_text'
    ];
    

    
    public function getBetTypeList()
    {
        return ['hh' => __('Hh'), 'spf' => __('Spf'), 'bf' => __('Bf'), 'zjq' => __('Zjq'), 'bqc' => __('Bqc')];
    }

    public function getStatusList()
    {
        return ['11' => __('Status 11')];
    }


    public function getBetTypeTextAttr($value, $data)
    {
        $value = $value ?: ($data['bet_type'] ?? '');
        $list = $this->getBetTypeList();
        return $list[$value] ?? '';
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ?: ($data['status'] ?? '');
        $list = $this->getStatusList();
        return $list[$value] ?? '';
    }


    // bet_data
    public function getBetDataAttr($value, $data)
    {
        return json_decode($value, true);
    }

}
