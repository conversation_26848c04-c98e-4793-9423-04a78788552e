<?php

namespace app\admin\model\lottery\issue;

use think\Model;
use traits\model\SoftDelete;

class Pls extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'lottery_issue_pls';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'sale_start_time_text',
        'sale_end_time_text',
        'draw_time_text'
    ];
    

    



    public function getSaleStartTimeTextAttr($value, $data)
    {
        $value = $value ?: ($data['sale_start_time'] ?? '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getSaleEndTimeTextAttr($value, $data)
    {
        $value = $value ?: ($data['sale_end_time'] ?? '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getDrawTimeTextAttr($value, $data)
    {
        $value = $value ?: ($data['draw_time'] ?? '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    protected function setSaleStartTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setSaleEndTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setDrawTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }


}
