<?php

namespace app\admin\model\lottery;

use think\Model;

class UnionPlan extends Model
{
    // 表名
    protected $name = 'lottery_union_plan';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';

    // 追加属性
    protected $append = [
        'status_text',
        'progress_percent'
    ];
    
    /**
     * 获取状态列表
     */
    public function getStatusList()
    {
        return [
            '0' => '进行中',
            '1' => '已满员',
            '2' => '已出票',
            '3' => '未满撤单'
        ];
    }
    
    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $status = $data['status'] ?? 0;
        $statusList = $this->getStatusList();
        return $statusList[$status] ?? '';
    }
    
    /**
     * 获取认购进度百分比
     */
    public function getProgressPercentAttr($value, $data)
    {
        $totalShares = $data['total_shares'] ?? 0;
        $purchasedShares = $data['purchased_shares'] ?? 0;
        
        if ($totalShares <= 0) {
            return 0;
        }
        
        return round(($purchasedShares / $totalShares) * 100, 2);
    }
    
    /**
     * 关联订单
     */
    public function order()
    {
        return $this->belongsTo('app\admin\model\lottery\order\Index', 'order_id', 'id');
    }
    
    /**
     * 关联参与记录
     */
    public function participations()
    {
        return $this->hasMany('app\admin\model\lottery\UnionParticipation', 'union_plan_id', 'id');
    }
}
