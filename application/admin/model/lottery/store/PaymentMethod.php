<?php

namespace app\admin\model\lottery\store;

use think\Model;
use traits\model\SoftDelete;

class PaymentMethod extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'lottery_store_payment_method';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'method_type_text'
    ];
    

    
    public function getMethodTypeList()
    {
        return ['alipay' => __('Alipay'), 'wechat' => __('Wechat'), 'bank_transfer' => __('Bank_transfer')];
    }


    public function getMethodTypeTextAttr($value, $data)
    {
        $value = $value ?: ($data['method_type'] ?? '');
        $list = $this->getMethodTypeList();
        return $list[$value] ?? '';
    }




}
