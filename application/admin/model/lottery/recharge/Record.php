<?php

namespace app\admin\model\lottery\recharge;

use think\Model;
use traits\model\SoftDelete;

class Record extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'lottery_recharge_record';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = 'deletetime';

    // 追加属性
    protected $append = [
        'status_text',
        'is_read_text',
        'payment_method_text',
        'payment_data_parsed',
        'payment_account_info'
    ];

    /**
     * 关联用户表
     */
    public function user()
    {
        return $this->hasOne('app\common\model\User', 'id', 'user_id');
    }
    

    
    public function getStatusList()
    {
        return ['0' => __('Status 0'), '1' => __('Status 1'), '2' => __('Status 2'), '3' => __('Status 3'), '4' => __('Status 4'), '5' => __('Status 5')];
    }

    public function getIsReadList()
    {
        return ['0' => __('Is_read 0'), '1' => __('Is_read 1')];
    }
    
    public function getPaymentMethodList()
    {
        return [
            'bank_transfer' => __('bank_transfer'),
            'wechat' => __('wechat'),
            'alipay' => __('alipay')
        ];
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ?: ($data['status'] ?? '');
        $list = $this->getStatusList();
        return $list[$value] ?? '';
    }


    public function getIsReadTextAttr($value, $data)
    {
        $value = $value ?: ($data['is_read'] ?? '');
        $list = $this->getIsReadList();
        return $list[$value] ?? '';
    }

    public function getPaymentMethodTextAttr($value, $data)
    {
        $value = $value ?: ($data['payment_method'] ?? '');
        $list = $this->getPaymentMethodList();
        return $list[$value] ?? $value;
    }

    /**
     * 解析 payment_data JSON 字段
     */
    public function getPaymentDataParsedAttr($value, $data)
    {
        if (empty($data['payment_data'])) {
            return null;
        }

        $paymentData = json_decode($data['payment_data'], true);
        return $paymentData ?: null;
    }

    /**
     * 获取收款账户信息（用于后台显示）
     */
    public function getPaymentAccountInfoAttr($value, $data)
    {
        // 优先使用 payment_data 快照
        if (!empty($data['payment_data'])) {
            $paymentData = json_decode($data['payment_data'], true);
            if ($paymentData) {
                return [
                    'source' => $paymentData['source'] ?? 'unknown',
                    'method_name' => $paymentData['method_name'] ?? '',
                    'method_type' => $paymentData['method_type'] ?? '',
                    'account_name' => $paymentData['account_name'] ?? '',
                    'account_number' => $paymentData['account_number'] ?? '',
                    'bank_name' => $paymentData['bank_name'] ?? '',
                    'bank_branch' => $paymentData['bank_branch'] ?? '',
                    'qr_code_url' => $paymentData['qr_code_url'] ?? '',
                    'snapshot_time' => $paymentData['snapshot_time'] ?? null,
                    'snapshot_source' => $paymentData['snapshot_source'] ?? 'unknown',
                    'is_snapshot' => true
                ];
            }
        }

        return [
            'source' => 'no_data',
            'method_name' => '无收款信息',
            'method_type' => $data['payment_method'] ?? '',
            'account_name' => '',
            'account_number' => '',
            'bank_name' => '',
            'bank_branch' => '',
            'qr_code_url' => '',
            'snapshot_time' => null,
            'snapshot_source' => 'none',
            'is_snapshot' => false
        ];
    }




}
