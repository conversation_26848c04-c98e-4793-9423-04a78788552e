<?php

namespace app\admin\model;

use think\Model;
use think\Session;

class Admin extends Model
{

    // 开启自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';
    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $hidden = [
        'password',
        'salt'
    ];

    // 追加属性
    protected $append = [
        'allowed_lottery_types_text'
    ];

    public static function init()
    {
        self::beforeWrite(function ($row) {
            $changed = $row->getChangedData();
            //如果修改了用户或或密码则需要重新登录
            if (isset($changed['username']) || isset($changed['password']) || isset($changed['salt'])) {
                $row->token = '';
            }
        });
    }

    /**
     * 关联商户模型
     */
    public function store()
    {
        return $this->belongsTo('addons\lottery\model\Store', 'store_id', 'id')->setEagerlyType(0);
    }

    /**
     * 获取彩种列表
     */
    public function getLotteryTypeList()
    {
        return [
            'football' => '足球',
            'basketball' => '篮球',
            'dlt' => '大乐透',
            'jq4' => '江西11选5',
            'bqc6' => '北京快乐8',
            'pls' => '排列三',
            'plw' => '排列五',
            'qxc' => '七星彩',
            'rx9' => '任选九',
            'sfc' => '胜负彩'
        ];
    }

    /**
     * 获取允许的彩种类型文本
     */
    public function getAllowedLotteryTypesTextAttr($value, $data)
    {
        $allowedTypes = $data['allowed_lottery_types'] ?? '';
        if (empty($allowedTypes)) {
            return '全部彩种';
        }
        
        $typeList = $this->getLotteryTypeList();
        $types = explode(',', $allowedTypes);
        $typeNames = [];
        
        foreach ($types as $type) {
            if (isset($typeList[$type])) {
                $typeNames[] = $typeList[$type];
            }
        }
        
        return empty($typeNames) ? '全部彩种' : implode(', ', $typeNames);
    }

    /**
     * 检查管理员是否可以操作指定彩种
     */
    public function canOperateLotteryType($lotteryType)
    {
        if (empty($this->allowed_lottery_types)) {
            return true; // 如果没有限制，则允许所有彩种
        }
        
        $allowedTypes = explode(',', $this->allowed_lottery_types);
        return in_array($lotteryType, $allowedTypes);
    }

    /**
     * 检查订单金额是否在允许范围内
     */
    public function canOperateOrderAmount($amount)
    {
        // 检查最小金额
        if ($this->min_order_amount > 0 && $amount < $this->min_order_amount) {
            return false;
        }
        
        // 检查最大金额
        if ($this->max_order_amount > 0 && $amount > $this->max_order_amount) {
            return false;
        }
        
        return true;
    }

}
