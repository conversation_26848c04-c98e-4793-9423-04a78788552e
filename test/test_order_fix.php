<?php
/**
 * 测试订单开奖修复
 * 验证BQC6等彩票类型的主订单状态和奖金更新功能
 */

require_once __DIR__ . '/../application/common.php';

use addons\lottery\model\Order;

echo "=== 测试订单开奖修复 ===" . PHP_EOL;

// 测试订单111的主订单状态更新
$orderId = 111;
echo "测试订单ID: {$orderId}" . PHP_EOL;

// 获取主订单信息
$mainOrder = Order::where('id', $orderId)->find();
if (!$mainOrder) {
    echo "错误：找不到订单 {$orderId}" . PHP_EOL;
    exit;
}

echo "订单类型: {$mainOrder->lottery_type}" . PHP_EOL;
echo "修复前 - 主订单奖金: {$mainOrder->prize_amount}" . PHP_EOL;
echo "修复前 - 主订单状态: {$mainOrder->status}" . PHP_EOL;

// 调用修复后的方法
echo PHP_EOL . "执行主订单状态和奖金更新..." . PHP_EOL;
$result = Order::updateMainOrderStatusAndPrize($orderId, '六场半全场');

if ($result) {
    echo "更新成功！" . PHP_EOL;
    
    // 重新获取主订单信息查看结果
    $updatedOrder = Order::where('id', $orderId)->find();
    echo "修复后 - 主订单奖金: {$updatedOrder->prize_amount}" . PHP_EOL;
    echo "修复后 - 主订单状态: {$updatedOrder->status}" . PHP_EOL;
} else {
    echo "更新失败！" . PHP_EOL;
}

echo PHP_EOL . "=== 测试完成 ===" . PHP_EOL;
