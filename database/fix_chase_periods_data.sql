-- 修复追号期次表数据不一致问题
-- 针对期号25081的数据修复
-- 
-- 问题：追号期次记录显示未中奖，但主订单显示已中奖
-- 原因：开奖功能中追号处理逻辑存在问题，导致追号记录未正确更新

USE lottery;

-- 1. 查看当前数据状态
SELECT '=== 修复前数据状态 ===' as info;

SELECT 
    '追号期次表' as table_name,
    id, order_id, issue_id, status, prize_amount, updatetime
FROM sys_lottery_chase_periods 
WHERE issue_id = 25081;

SELECT 
    '主订单表' as table_name,
    id, order_no, status, prize_amount
FROM sys_lottery_order 
WHERE id = 1;

SELECT 
    '大乐透子订单表' as table_name,
    id, order_id, issue_id, status, prize_amount, bet_multiple
FROM sys_lottery_order_dlt 
WHERE order_id = 1 AND issue_id = 25081;

-- 2. 分析中奖情况
-- 从子订单表中计算实际的中奖金额
SELECT 
    '中奖分析' as info,
    order_id,
    SUM(prize_amount) as total_prize,
    COUNT(*) as total_suborders,
    SUM(CASE WHEN prize_amount > 0 THEN 1 ELSE 0 END) as winning_suborders
FROM sys_lottery_order_dlt 
WHERE order_id = 1 AND issue_id = 25081
GROUP BY order_id;

-- 3. 修复追号期次记录
-- 根据主订单的中奖情况更新追号期次记录

UPDATE sys_lottery_chase_periods cp
SET 
    status = CASE 
        WHEN (
            SELECT SUM(prize_amount) 
            FROM sys_lottery_order_dlt 
            WHERE order_id = cp.order_id AND issue_id = cp.issue_id
        ) > 0 THEN 1  -- 已中奖
        ELSE 2        -- 未中奖
    END,
    prize_amount = (
        SELECT COALESCE(SUM(prize_amount), 0) 
        FROM sys_lottery_order_dlt 
        WHERE order_id = cp.order_id AND issue_id = cp.issue_id
    ),
    updatetime = UNIX_TIMESTAMP()
WHERE issue_id = 25081;

-- 4. 验证修复结果
SELECT '=== 修复后数据状态 ===' as info;

SELECT 
    '追号期次表(修复后)' as table_name,
    id, order_id, issue_id, status, prize_amount, updatetime
FROM sys_lottery_chase_periods 
WHERE issue_id = 25081;

-- 5. 数据一致性检查
SELECT 
    '数据一致性检查' as info,
    cp.id as chase_period_id,
    cp.order_id,
    cp.issue_id,
    cp.status as chase_status,
    cp.prize_amount as chase_prize,
    mo.status as main_order_status,
    mo.prize_amount as main_order_prize,
    CASE 
        WHEN cp.status = 1 AND mo.status = 3 AND cp.prize_amount = mo.prize_amount THEN '✅ 一致'
        WHEN cp.status = 2 AND mo.status = 4 AND cp.prize_amount = 0 THEN '✅ 一致'
        ELSE '❌ 不一致'
    END as consistency_check
FROM sys_lottery_chase_periods cp
JOIN sys_lottery_order mo ON cp.order_id = mo.id
WHERE cp.issue_id = 25081;

-- 6. 追号汇总统计更新（如果需要）
-- 更新追号汇总表的统计信息
UPDATE sys_lottery_chase_summary cs
SET 
    completed_periods = (
        SELECT COUNT(*) 
        FROM sys_lottery_chase_periods 
        WHERE order_id = cs.order_id AND status IN (1, 2)
    ),
    win_periods = (
        SELECT COUNT(*) 
        FROM sys_lottery_chase_periods 
        WHERE order_id = cs.order_id AND status = 1
    ),
    total_prize = (
        SELECT COALESCE(SUM(prize_amount), 0) 
        FROM sys_lottery_chase_periods 
        WHERE order_id = cs.order_id
    ),
    updatetime = UNIX_TIMESTAMP()
WHERE order_id = 1;

-- 7. 最终验证
SELECT 
    '最终验证结果' as info,
    cp.id,
    cp.order_id,
    cp.issue_id,
    CASE cp.status 
        WHEN 0 THEN '待开奖'
        WHEN 1 THEN '已中奖'
        WHEN 2 THEN '未中奖'
        WHEN 3 THEN '已取消'
        ELSE '未知状态'
    END as chase_status_text,
    cp.prize_amount as chase_prize,
    CASE mo.status
        WHEN 0 THEN '未付款'
        WHEN 1 THEN '待出票'
        WHEN 2 THEN '待开奖'
        WHEN 3 THEN '已中奖'
        WHEN 4 THEN '未中奖'
        ELSE '未知状态'
    END as main_order_status_text,
    mo.prize_amount as main_order_prize
FROM sys_lottery_chase_periods cp
JOIN sys_lottery_order mo ON cp.order_id = mo.id
WHERE cp.issue_id = 25081;

-- 执行说明：
-- 1. 此脚本会自动修复期号25081的追号期次数据不一致问题
-- 2. 修复逻辑：根据对应子订单的实际中奖情况更新追号记录
-- 3. 如果子订单有奖金，追号记录状态设为"已中奖"，奖金设为子订单奖金总和
-- 4. 如果子订单无奖金，追号记录状态设为"未中奖"，奖金设为0
-- 5. 同时更新追号汇总统计信息
