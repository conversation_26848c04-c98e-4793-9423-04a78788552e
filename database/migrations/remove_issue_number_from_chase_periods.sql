-- 彩票追号功能数据结构优化
-- 移除 sys_lottery_chase_periods 表中冗余的 issue_number 字段
-- 
-- 执行前请备份数据库！
-- 
-- 设计原则：
-- 1. issue_id 是期号的唯一标识符，足以确定具体期次
-- 2. 期号的字符串表示应该通过关联查询从期号表获取
-- 3. 避免在多个表中重复存储相同的期号信息，确保数据一致性

USE lottery;

-- 1. 检查当前表结构
DESCRIBE sys_lottery_chase_periods;

-- 2. 检查是否存在依赖 issue_number 字段的索引
SHOW INDEX FROM sys_lottery_chase_periods WHERE Column_name = 'issue_number';

-- 3. 备份现有数据（可选，建议在生产环境执行）
-- CREATE TABLE sys_lottery_chase_periods_backup AS SELECT * FROM sys_lottery_chase_periods;

-- 4. 移除 issue_number 字段
-- 注意：这个操作不可逆，请确保已经备份数据
ALTER TABLE sys_lottery_chase_periods DROP COLUMN issue_number;

-- 5. 验证修改结果
DESCRIBE sys_lottery_chase_periods;

-- 6. 检查数据完整性
-- 验证所有 issue_id 都能在对应的期号表中找到
SELECT 
    cp.lottery_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN cp.lottery_type = 'dlt' AND i_dlt.id IS NULL THEN 1 END) as missing_dlt,
    COUNT(CASE WHEN cp.lottery_type = 'pls' AND i_pls.id IS NULL THEN 1 END) as missing_pls,
    COUNT(CASE WHEN cp.lottery_type = 'plw' AND i_plw.id IS NULL THEN 1 END) as missing_plw,
    COUNT(CASE WHEN cp.lottery_type = 'qxc' AND i_qxc.id IS NULL THEN 1 END) as missing_qxc
FROM sys_lottery_chase_periods cp
LEFT JOIN sys_lottery_issue_dlt i_dlt ON cp.lottery_type = 'dlt' AND cp.issue_id = i_dlt.id
LEFT JOIN sys_lottery_issue_pls i_pls ON cp.lottery_type = 'pls' AND cp.issue_id = i_pls.id
LEFT JOIN sys_lottery_issue_plw i_plw ON cp.lottery_type = 'plw' AND cp.issue_id = i_plw.id
LEFT JOIN sys_lottery_issue_qxc i_qxc ON cp.lottery_type = 'qxc' AND cp.issue_id = i_qxc.id
GROUP BY cp.lottery_type;

-- 7. 优化建议：添加复合索引提高查询性能
-- 为常用的查询组合创建索引
CREATE INDEX idx_chase_periods_lottery_issue ON sys_lottery_chase_periods(lottery_type, issue_id);
CREATE INDEX idx_chase_periods_order_status ON sys_lottery_chase_periods(order_id, status);
CREATE INDEX idx_chase_periods_user_lottery ON sys_lottery_chase_periods(user_id, lottery_type, status);

-- 8. 显示最终的表结构
SHOW CREATE TABLE sys_lottery_chase_periods;

-- 执行完成后的验证步骤：
-- 1. 确认 issue_number 字段已被移除
-- 2. 确认所有 issue_id 都有对应的期号记录
-- 3. 测试追号功能是否正常工作
-- 4. 测试开奖功能是否正常处理追号记录

-- 注意事项：
-- 1. 此脚本会永久删除 issue_number 字段，请确保代码已经更新
-- 2. 建议在测试环境先执行并验证
-- 3. 生产环境执行前请做好完整备份
-- 4. 执行后需要重启应用服务以清除模型缓存
