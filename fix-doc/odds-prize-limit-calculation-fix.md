# 赔率修改奖金限额计算功能修复

## 📋 问题描述

在彩票系统后端修改赔率功能中，发现重新计算预测奖金时没有正确应用奖金限额规则。原有的 `recalculateOrderAmount` 方法使用了简化的赔率乘法计算，没有调用对应彩种的奖金计算类，导致：

1. **奖金上限未应用**：高赔率场景下可能计算出超过系统设定上限的奖金
2. **计算逻辑不一致**：修改赔率后的奖金计算与新建订单时的计算逻辑不同
3. **投注类型未区分**：没有根据不同投注类型（单关、串关等）应用相应的奖金限额

## 🎯 修复目标

1. 确保修改赔率后重新计算奖金时正确应用奖金限额规则
2. 使用对应彩种的专业奖金计算方法
3. 保持与新建订单时奖金计算逻辑的一致性
4. 支持不同投注类型的差异化奖金限额

## 🔧 具体修改

### 1. 修改 `updateOrderOdds` 方法

**文件**：`addons/lottery/model/store/Order.php`

**修改位置**：第429-436行

**修改前**：
```php
// 重新计算订单金额
$calculationResult = self::recalculateOrderAmount($updatedBetData, $betMultiple, $lotteryType);
```

**修改后**：
```php
// 获取投注方式数据
$betMethod = $subOrder['bet_method'] ?? [];
if (is_string($betMethod)) {
    $betMethod = json_decode($betMethod, true);
}

// 重新计算订单金额，使用正确的奖金限额计算逻辑
$calculationResult = self::recalculateOrderAmount($updatedBetData, $betMultiple, $lotteryType, $betMethod);
```

### 2. 重构 `recalculateOrderAmount` 方法

**文件**：`addons/lottery/model/store/Order.php`

**修改位置**：第581-647行

**主要改进**：

1. **添加 `$betMethod` 参数**：
   ```php
   private static function recalculateOrderAmount($betData, $multiple, $lotteryType, $betMethod = [])
   ```

2. **集成专业奖金计算类**：
   ```php
   if ($lotteryType === 'football') {
       // 使用足球奖金计算方法
       $prizeResult = \addons\lottery\library\order\Football::calculatePotentialPrize($betData, $betMethod, $multiple);
       $potentialWin = $prizeResult['max'] ?? 0;
   } elseif ($lotteryType === 'basketball') {
       // 使用篮球奖金计算方法
       $prizeResult = \addons\lottery\library\order\Basketball::calculatePotentialPrize($betData, $betMethod, $multiple);
       $potentialWin = $prizeResult['max'] ?? 0;
   }
   ```

3. **添加异常处理**：
   ```php
   try {
       // 奖金计算逻辑
   } catch (\Exception $e) {
       // 如果奖金计算失败，使用简化计算作为备用
       $totalOdds = 1;
       foreach ($betData as $bet) {
           $totalOdds *= floatval($bet['odds']);
       }
       $potentialWin = $totalAmount * $totalOdds;
   }
   ```

## 🏆 奖金限额规则

### 足球竞彩奖金限额

| 投注类型 | 奖金上限 | 说明 |
|---------|---------|------|
| 单关 | 10万元 | 单场投注最高奖金限额 |
| 2-3场过关 | 20万元 | 2场和3场过关投注限额 |
| 4-5场过关 | 50万元 | 4场和5场过关投注限额 |
| 6场以上过关 | 100万元 | 6场及以上过关投注限额 |

### 篮球竞彩奖金限额

| 投注类型 | 奖金上限 | 说明 |
|---------|---------|------|
| 所有投注类型 | 20万元 | 篮球竞彩统一奖金限额 |

## 🧪 测试验证

### 测试文件

创建了 `test/test-odds-prize-limit-calculation.js` 测试文件，包含：

1. **高赔率场景测试**：验证超高赔率是否正确应用奖金上限
2. **不同投注类型测试**：验证各种投注方式的奖金限额
3. **边界值测试**：测试接近奖金上限的场景

### 测试用例

```javascript
const testCases = [
  {
    name: '足球单关高赔率测试',
    oddsData: {
      'match1_spf_3': 150.0, // 高赔率：150倍
      'match2_spf_1': 200.0  // 高赔率：200倍
    },
    expectedMaxPrize: 100000, // 应用10万元上限
    description: '足球单关投注，应用10万元奖金上限'
  },
  {
    name: '篮球单关高赔率测试',
    oddsData: {
      'match1_sf_1': 300.0,  // 高赔率：300倍
      'match2_rsf_0': 250.0  // 高赔率：250倍
    },
    expectedMaxPrize: 200000, // 应用20万元上限
    description: '篮球单关投注，应用20万元奖金上限'
  }
];
```

## 📊 影响范围

### 修改文件
- `addons/lottery/model/store/Order.php` - 核心奖金计算逻辑修复

### 新增文件
- `test/test-odds-prize-limit-calculation.js` - 奖金限额测试文件
- `fix-doc/odds-prize-limit-calculation-fix.md` - 本修复文档

### 依赖关系
- 依赖 `addons/lottery/library/order/Football.php` 的 `calculatePotentialPrize` 方法
- 依赖 `addons/lottery/library/order/Basketball.php` 的 `calculatePotentialPrize` 方法

## ✅ 验证清单

- [x] 修改 `updateOrderOdds` 方法获取 `bet_method` 数据
- [x] 重构 `recalculateOrderAmount` 方法集成专业奖金计算
- [x] 添加足球和篮球奖金计算类调用
- [x] 实现异常处理和备用计算逻辑
- [x] 创建全面的测试用例
- [x] 编写详细的修复文档

## 🚀 部署说明

### 部署前检查
1. 确保 `Football.php` 和 `Basketball.php` 奖金计算类正常工作
2. 验证数据库中订单的 `bet_method` 字段数据格式
3. 检查现有订单的奖金计算是否正常

### 部署步骤
1. 备份现有的 `Order.php` 文件
2. 部署修改后的代码
3. 运行测试用例验证功能
4. 监控线上订单的奖金计算结果

### 回滚方案
如果发现问题，可以快速回滚到原有的简化计算逻辑：
```php
// 简化计算逻辑（回滚用）
$totalOdds = 1;
foreach ($betData as $bet) {
    $totalOdds *= floatval($bet['odds']);
}
$potentialWin = $totalAmount * $totalOdds;
```

## 📝 后续优化

1. **性能优化**：考虑缓存奖金计算结果
2. **监控告警**：添加奖金计算异常的监控
3. **日志记录**：记录奖金计算的详细过程
4. **配置化**：将奖金限额配置化，便于调整

## 📅 更新信息

- **更新日期**：2025-01-01
- **更新人员**：系统管理员
- **版本影响**：后端核心计算逻辑
- **测试状态**：待验证
- **风险等级**：中等（涉及核心业务逻辑）
