#!/bin/bash

# 合买守护进程管理脚本
# 使用方法: bash scripts/union_daemon_manager.sh {start|stop|restart|status|install}

# 获取项目根目录
PROJECT_ROOT=$(cd "$(dirname "$0")/.." && pwd)
PHP_PATH=$(which php)
DAEMON_NAME="lottery:union:daemon"
SERVICE_NAME="lottery-union-daemon"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查环境
check_environment() {
    if [ ! -f "$PHP_PATH" ]; then
        log_error "未找到PHP可执行文件"
        exit 1
    fi
    
    if [ ! -f "$PROJECT_ROOT/think" ]; then
        log_error "未找到think命令文件"
        exit 1
    fi
    
    if [ ! -f "$PROJECT_ROOT/addons/lottery/command/UnionDaemon.php" ]; then
        log_error "未找到UnionDaemon.php文件"
        exit 1
    fi
}

# 启动守护进程
start_daemon() {
    log_info "启动合买守护进程..."
    
    # 检查是否已在运行
    if is_daemon_running; then
        log_warn "守护进程已在运行中"
        return 1
    fi
    
    # 创建日志目录
    LOG_DIR="$PROJECT_ROOT/runtime/log/union_daemon"
    mkdir -p "$LOG_DIR"
    
    # 启动守护进程（后台运行）
    cd "$PROJECT_ROOT"
    nohup $PHP_PATH think $DAEMON_NAME > "$LOG_DIR/daemon.log" 2>&1 &
    
    # 等待启动
    sleep 3
    
    if is_daemon_running; then
        local pid=$(get_daemon_pid)
        log_info "守护进程启动成功，PID: $pid"
        log_info "日志文件: $LOG_DIR/daemon.log"
        return 0
    else
        log_error "守护进程启动失败"
        return 1
    fi
}

# 停止守护进程
stop_daemon() {
    log_info "停止合买守护进程..."
    
    if ! is_daemon_running; then
        log_warn "守护进程未在运行"
        return 1
    fi
    
    cd "$PROJECT_ROOT"
    $PHP_PATH think $DAEMON_NAME --stop
    
    # 等待停止
    local timeout=15
    while [ $timeout -gt 0 ] && is_daemon_running; do
        sleep 1
        timeout=$((timeout - 1))
    done
    
    if is_daemon_running; then
        log_error "守护进程停止失败，尝试强制停止"
        local pid=$(get_daemon_pid)
        if [ -n "$pid" ]; then
            kill -9 "$pid" 2>/dev/null
        fi
    fi
    
    if ! is_daemon_running; then
        log_info "守护进程已停止"
        return 0
    else
        log_error "守护进程停止失败"
        return 1
    fi
}

# 重启守护进程
restart_daemon() {
    log_info "重启合买守护进程..."
    stop_daemon
    sleep 2
    start_daemon
}

# 查看守护进程状态
show_status() {
    echo "=== 合买守护进程状态 ==="
    
    cd "$PROJECT_ROOT"
    $PHP_PATH think $DAEMON_NAME --status
    
    echo ""
    echo "=== 系统信息 ==="
    echo "项目路径: $PROJECT_ROOT"
    echo "PHP路径: $PHP_PATH"
    echo "当前时间: $(date '+%Y-%m-%d %H:%M:%S')"
    
    # 显示最近的日志
    local log_file="$PROJECT_ROOT/runtime/log/union_daemon/daemon.log"
    if [ -f "$log_file" ]; then
        echo ""
        echo "=== 最近日志 (最后10行) ==="
        tail -n 10 "$log_file"
    fi
}

# 检查守护进程是否在运行
is_daemon_running() {
    local pid_file="$PROJECT_ROOT/runtime/union_daemon.pid"
    
    if [ ! -f "$pid_file" ]; then
        return 1
    fi
    
    local pid=$(cat "$pid_file")
    
    if [ -z "$pid" ]; then
        return 1
    fi
    
    # 检查进程是否存在
    if kill -0 "$pid" 2>/dev/null; then
        return 0
    else
        # PID文件存在但进程不存在，清理PID文件
        rm -f "$pid_file"
        return 1
    fi
}

# 获取守护进程PID
get_daemon_pid() {
    local pid_file="$PROJECT_ROOT/runtime/union_daemon.pid"
    
    if [ -f "$pid_file" ]; then
        cat "$pid_file"
    fi
}

# 安装系统服务
install_service() {
    log_info "安装系统服务..."
    
    # 检查是否为root用户
    if [ "$EUID" -ne 0 ]; then
        log_error "安装系统服务需要root权限"
        exit 1
    fi
    
    # 创建systemd服务文件
    local service_file="/etc/systemd/system/${SERVICE_NAME}.service"
    
    cat > "$service_file" << EOF
[Unit]
Description=Lottery Union Daemon
After=network.target mysql.service

[Service]
Type=simple
User=www-data
Group=www-data
WorkingDirectory=$PROJECT_ROOT
ExecStart=$PHP_PATH think $DAEMON_NAME
ExecStop=$PHP_PATH think $DAEMON_NAME --stop
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF

    # 重新加载systemd
    systemctl daemon-reload
    
    # 启用服务
    systemctl enable "$SERVICE_NAME"
    
    log_info "系统服务安装完成"
    log_info "使用以下命令管理服务:"
    log_info "  启动: systemctl start $SERVICE_NAME"
    log_info "  停止: systemctl stop $SERVICE_NAME"
    log_info "  重启: systemctl restart $SERVICE_NAME"
    log_info "  状态: systemctl status $SERVICE_NAME"
    log_info "  日志: journalctl -u $SERVICE_NAME -f"
}

# 卸载系统服务
uninstall_service() {
    log_info "卸载系统服务..."
    
    if [ "$EUID" -ne 0 ]; then
        log_error "卸载系统服务需要root权限"
        exit 1
    fi
    
    local service_file="/etc/systemd/system/${SERVICE_NAME}.service"
    
    # 停止并禁用服务
    systemctl stop "$SERVICE_NAME" 2>/dev/null || true
    systemctl disable "$SERVICE_NAME" 2>/dev/null || true
    
    # 删除服务文件
    if [ -f "$service_file" ]; then
        rm -f "$service_file"
        systemctl daemon-reload
        log_info "系统服务已卸载"
    else
        log_warn "系统服务文件不存在"
    fi
}

# 显示帮助信息
show_help() {
    echo "合买守护进程管理脚本"
    echo ""
    echo "使用方法: $0 {start|stop|restart|status|install|uninstall|help}"
    echo ""
    echo "命令说明:"
    echo "  start      启动守护进程"
    echo "  stop       停止守护进程"
    echo "  restart    重启守护进程"
    echo "  status     查看守护进程状态"
    echo "  install    安装为系统服务 (需要root权限)"
    echo "  uninstall  卸载系统服务 (需要root权限)"
    echo "  help       显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 start                    # 启动守护进程"
    echo "  $0 status                   # 查看状态"
    echo "  sudo $0 install             # 安装系统服务"
    echo ""
    echo "日志位置: $PROJECT_ROOT/runtime/log/union_daemon/"
    echo "配置文件: $PROJECT_ROOT/addons/lottery/config/union_expire.php"
}

# 主函数
main() {
    # 检查参数
    if [ $# -eq 0 ]; then
        show_help
        exit 1
    fi
    
    # 检查环境（除了help命令）
    if [ "$1" != "help" ]; then
        check_environment
    fi
    
    # 执行命令
    case "$1" in
        start)
            start_daemon
            ;;
        stop)
            stop_daemon
            ;;
        restart)
            restart_daemon
            ;;
        status)
            show_status
            ;;
        install)
            install_service
            ;;
        uninstall)
            uninstall_service
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
