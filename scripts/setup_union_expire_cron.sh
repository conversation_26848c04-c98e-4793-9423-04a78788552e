#!/bin/bash

# 合买到期检测定时任务设置脚本
# 使用方法: bash scripts/setup_union_expire_cron.sh

# 获取项目根目录
PROJECT_ROOT=$(cd "$(dirname "$0")/.." && pwd)
PHP_PATH=$(which php)

echo "=== 合买到期检测定时任务设置 ==="
echo "项目路径: $PROJECT_ROOT"
echo "PHP路径: $PHP_PATH"
echo ""

# 检查PHP路径
if [ ! -f "$PHP_PATH" ]; then
    echo "错误: 未找到PHP可执行文件"
    exit 1
fi

# 检查项目路径
if [ ! -f "$PROJECT_ROOT/think" ]; then
    echo "错误: 未找到think命令文件"
    exit 1
fi

# 创建日志目录
LOG_DIR="$PROJECT_ROOT/runtime/log/union_expire"
mkdir -p "$LOG_DIR"

echo "创建日志目录: $LOG_DIR"

# 定时任务配置
CRON_JOBS="
# 合买到期检测 - 每5分钟执行一次
*/5 * * * * cd $PROJECT_ROOT && $PHP_PATH think lottery:union:expire >> $LOG_DIR/expire.log 2>&1

# 合买监控报告 - 每小时执行一次
0 * * * * cd $PROJECT_ROOT && $PHP_PATH think lottery:union:monitor >> $LOG_DIR/monitor.log 2>&1

# 日志清理 - 每天凌晨2点执行
0 2 * * * find $LOG_DIR -name '*.log' -mtime +30 -delete
"

# 备份当前crontab
echo "备份当前crontab..."
crontab -l > "$PROJECT_ROOT/runtime/crontab_backup_$(date +%Y%m%d_%H%M%S).txt" 2>/dev/null || echo "当前无crontab任务"

# 添加新的定时任务
echo "添加合买到期检测定时任务..."
(crontab -l 2>/dev/null; echo "$CRON_JOBS") | crontab -

echo ""
echo "=== 设置完成 ==="
echo "已添加以下定时任务:"
echo "1. 合买到期检测: 每5分钟执行一次"
echo "2. 合买监控报告: 每小时执行一次"
echo "3. 日志清理: 每天凌晨2点执行"
echo ""
echo "日志文件位置:"
echo "- 到期检测日志: $LOG_DIR/expire.log"
echo "- 监控报告日志: $LOG_DIR/monitor.log"
echo ""
echo "手动执行命令:"
echo "- 到期检测: cd $PROJECT_ROOT && php think lottery:union:expire"
echo "- 监控报告: cd $PROJECT_ROOT && php think lottery:union:monitor"
echo "- 试运行: cd $PROJECT_ROOT && php think lottery:union:expire --dry-run"
echo ""
echo "查看当前crontab: crontab -l"
echo "编辑crontab: crontab -e"
echo ""

# 测试命令是否可用
echo "=== 测试命令 ==="
cd "$PROJECT_ROOT"

echo "测试到期检测命令..."
if $PHP_PATH think lottery:union:expire --dry-run >/dev/null 2>&1; then
    echo "✅ 到期检测命令测试成功"
else
    echo "❌ 到期检测命令测试失败"
fi

echo "测试监控命令..."
if $PHP_PATH think lottery:union:monitor >/dev/null 2>&1; then
    echo "✅ 监控命令测试成功"
else
    echo "❌ 监控命令测试失败"
fi

echo ""
echo "=== 建议 ==="
echo "1. 首次运行建议使用试运行模式: php think lottery:union:expire --dry-run"
echo "2. 定期检查日志文件确保正常运行"
echo "3. 可以通过监控命令查看处理统计: php think lottery:union:monitor"
echo "4. 如需调整执行频率，请编辑crontab: crontab -e"
echo ""
