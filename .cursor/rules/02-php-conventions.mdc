# PHP Coding Conventions

## Class Structure

- Classes follow PSR-4 autoloading standard
- Namespaces match directory structure
- Class files should have the same name as the class

## Controller Pattern

- Controllers extend base controller classes
- Methods named with camelCase (e.g., `indexAction`, `detailAction`)
- Controller actions return J<PERSON><PERSON> for API or HTML for UI

## Model Pattern

- Models follow Active Record pattern
- Table names are inferred from model names (pluralized)
- Primary key is assumed to be `id` unless specified otherwise

## File Organization

- One class per file
- Use proper namespaces
- Follow ThinkPHP framework conventions

## Error Handling

- Use exceptions for error handling
- Custom exceptions extend from base Exception classes
- API errors should return proper HTTP status codes
