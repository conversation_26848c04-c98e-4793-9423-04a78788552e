---
description:
globs:
alwaysApply: false
---
# 彩票管理系统数据模型

彩票管理系统的数据模型位于 `addons/lottery/model` 目录中，所有模型继承自 [Base模型](mdc:addons/lottery/model/Base.php)。

## 核心数据模型

- [User](mdc:addons/lottery/model/User.php) - 用户模型，管理用户信息
- [Order](mdc:addons/lottery/model/Order.php) - 订单模型，管理彩票订单
- [MoneyLog](mdc:addons/lottery/model/MoneyLog.php) - 资金流水模型，记录用户资金变动
- [Store](mdc:addons/lottery/model/Store.php) - 商店模型
- [RechargeRecord](mdc:addons/lottery/model/RechargeRecord.php) - 充值记录模型

## 聊天相关模型

- [ChatMessage](mdc:addons/lottery/model/ChatMessage.php) - 聊天消息模型
- [ChatFriend](mdc:addons/lottery/model/ChatFriend.php) - 好友关系模型
- [ChatConversation](mdc:addons/lottery/model/ChatConversation.php) - 会话模型
- [ChatConversationMember](mdc:addons/lottery/model/ChatConversationMember.php) - 会话成员模型

## 比赛相关模型

- [MatchFootball](mdc:addons/lottery/model/MatchFootball.php) - 足球比赛模型
- [MatchBasketball](mdc:addons/lottery/model/MatchBasketball.php) - 篮球比赛模型

## 跟单相关模型

- [FollowPlan](mdc:addons/lottery/model/FollowPlan.php) - 跟单计划模型
- [FollowRecord](mdc:addons/lottery/model/FollowRecord.php) - 跟单记录模型

## 模型关系图

User (用户) → Order (订单)
User (用户) → MoneyLog (资金流水)
User (用户) → RechargeRecord (充值记录)
User (用户) → ChatFriend (好友关系)
User (用户) → ChatConversationMember (会话成员) → ChatConversation (会话)
ChatConversation (会话) → ChatMessage (聊天消息)
