---
description: 
globs: 
alwaysApply: false
---
# 彩票管理API接口

彩票管理系统的核心API接口位于 `addons/lottery/controller` 目录中。所有API控制器继承自 [Base控制器](mdc:addons/lottery/controller/Base.php)，Base控制器继承自系统的API基类。

## 主要API控制器

- [Index](mdc:addons/lottery/controller/Index.php) - 基础功能接口，包括文件上传等
- [User](mdc:addons/lottery/controller/User.php) - 用户管理接口
- [Order](mdc:addons/lottery/controller/Order.php) - 订单管理接口
- [MoneyLog](mdc:addons/lottery/controller/MoneyLog.php) - 资金流水接口
- [Store](mdc:addons/lottery/controller/Store.php) - 商店相关接口
- [RechargeRecord](mdc:addons/lottery/controller/RechargeRecord.php) - 充值记录接口

## 聊天相关API

- [ChatMessage](mdc:addons/lottery/controller/ChatMessage.php) - 聊天消息接口
- [ChatFriend](mdc:addons/lottery/controller/ChatFriend.php) - 好友管理接口
- [ChatConversation](mdc:addons/lottery/controller/ChatConversation.php) - 会话管理接口

## 比赛相关API

- [MatchFootball](mdc:addons/lottery/controller/MatchFootball.php) - 足球比赛接口
- [MatchBasketball](mdc:addons/lottery/controller/MatchBasketball.php) - 篮球比赛接口

## API使用说明

所有API请求需要在URL中包含addon参数，例如：`/api/lottery/user/login`，其中：
- `/api` 是API基础路径
- `/lottery` 是插件名称
- `/user` 是控制器名称
- `/login` 是方法名称

## 控制器规则
- 控制器中不能写try catch 
- 返回数据使用$this->success $this->error
- 操作方法要写入model里，在控制器里调用

## 模型谷子额
- 尽可能使用静态函数
- 如需返回报错，使用 addons/lottery/library/Exception.php



