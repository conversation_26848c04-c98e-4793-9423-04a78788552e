---
description:
globs:
alwaysApply: false
---
# 项目概览

这是一个基于ThinkPHP框架的彩票管理后台系统。主要功能包括用户管理、订单管理、资金流水、聊天功能以及比赛管理等。

## 主要目录结构

- [addons/lottery](mdc:addons/lottery) - 彩票管理核心插件目录
  - [addons/lottery/controller](mdc:addons/lottery/controller) - API控制器目录
  - [addons/lottery/model](mdc:addons/lottery/model) - 数据模型目录
  - [addons/lottery/config](mdc:addons/lottery/config) - 配置文件目录

- [addons/nessms](mdc:addons/nessms) - 短信服务插件目录

- [application/admin](mdc:application/admin) - 管理后台应用目录
  - [application/admin/controller](mdc:application/admin/controller) - 管理后台控制器
  - [application/admin/view](mdc:application/admin/view) - 管理后台视图

- [public](mdc:public) - 静态资源目录

## 核心入口文件

- [addons/lottery/Lottery.php](mdc:addons/lottery/Lottery.php) - 彩票插件主入口
- [addons/lottery/controller/Base.php](mdc:addons/lottery/controller/Base.php) - API基础控制器
