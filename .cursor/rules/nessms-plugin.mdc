---
description:
globs:
alwaysApply: false
---
# 短信服务插件 (Nessms)

彩票管理系统中的短信服务功能由 `addons/nessms` 插件提供，基于网易云信短信服务。

## 核心文件

- [Nessms.php](mdc:addons/nessms/Nessms.php) - 短信插件入口文件
- [Nessms类](mdc:addons/nessms/library/Nessms.php) - 短信发送核心类库

## 使用方法

短信服务的核心功能由 `\addons\nessms\library\Nessms` 类提供，使用链式调用方式：

```php
$nessms = new \addons\nessms\library\Nessms();
$result = $nessms->mobile('13800138000')    // 设置手机号
               ->template('模板ID')         // 设置短信模板ID
               ->code('验证码')             // 设置验证码内容
               ->sendSmsCode();            // 发送短信
```

## 配置

短信服务的配置信息存储在插件配置中，可以通过 `get_addon_config('nessms')` 获取：

- app_key: 网易云信应用的AppKey
- app_secret: 网易云信应用的AppSecret

## 调用流程

1. 实例化 `\addons\nessms\library\Nessms` 类
2. 设置手机号、模板ID和验证码
3. 调用 `sendSmsCode()` 方法发送短信
4. 返回布尔值表示发送结果
