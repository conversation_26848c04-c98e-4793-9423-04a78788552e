# Code Fixes and Known Issues

## Fix Documentation

Important fixes are documented in:
- `/fix-doc/odds-prize-limit-calculation-fix.md`
- `/fix-doc/payment-method-api-fix.md`
- `/fix-doc/payment-method-management-implementation.md`

## Backend Fixes

- Array offset error fixed in `/tests/backend/backend-array-offset-error-fix.md`
- Register button style fixed in `/tests/register-button-style-fix.md`

## Test Files

Test files that demonstrate fixes:
- `/test/test-model-fix.php`
- `/test/test-model-refactor.php`
- `/test/verify-navbar-color-consistency.js`

## Working with Model Refactoring

When working with models, especially for RechargeRecord:
- `/addons/lottery/model/RechargeRecord.php`
- `/addons/lottery/model/store/RechargeRecord.php`
- `/addons/lottery/controller/RechargeRecord.php`
- `/addons/lottery/controller/store/RechargeRecord.php`

Pay attention to recent changes in these files as they're currently being modified.

## Git Status

Files being modified:
- Controller and model files related to RechargeRecord 
- Test files related to model fixes and refactoring
